# coding: utf-8
# 令牌生成器 - 生成类似令牌.xlsx格式的令牌表格
# File: token_generator.py

import pandas as pd
import uuid
import random
import time
from datetime import datetime
import argparse
import os

class TokenGenerator:
    """令牌生成器类"""
    
    def __init__(self):
        self.generated_tokens = set()  # 避免重复
        
    def generate_uuid_token(self):
        """生成UUID格式的令牌"""
        return str(uuid.uuid4())
    
    def generate_custom_token(self, pattern="xxxxxxxx-xxxx-11f0-xxxx-xxxxxxxxxxxx"):
        """
        生成自定义格式的令牌
        pattern中的x会被随机十六进制字符替换
        """
        result = ""
        for char in pattern:
            if char == 'x':
                result += random.choice('0123456789abcdef')
            else:
                result += char
        return result
    
    def generate_similar_token(self, base_token):
        """
        基于现有令牌生成相似格式的令牌
        保持相同的结构但改变部分字符
        """
        parts = base_token.split('-')
        if len(parts) != 5:
            return self.generate_uuid_token()
        
        # 保持第三部分不变（如11f0），随机化其他部分
        new_parts = []
        for i, part in enumerate(parts):
            if i == 2:  # 保持第三部分
                new_parts.append(part)
            else:
                new_part = ''.join(random.choice('0123456789abcdef') for _ in range(len(part)))
                new_parts.append(new_part)
        
        return '-'.join(new_parts)
    
    def generate_tokens(self, count, method='uuid', base_token=None, avoid_duplicates=True):
        """
        生成指定数量的令牌
        
        Args:
            count: 生成数量
            method: 生成方法 ('uuid', 'custom', 'similar')
            base_token: 基础令牌（用于similar方法）
            avoid_duplicates: 是否避免重复
        """
        tokens = []
        attempts = 0
        max_attempts = count * 10  # 防止无限循环
        
        while len(tokens) < count and attempts < max_attempts:
            attempts += 1
            
            if method == 'uuid':
                token = self.generate_uuid_token()
            elif method == 'custom':
                token = self.generate_custom_token()
            elif method == 'similar' and base_token:
                token = self.generate_similar_token(base_token)
            else:
                token = self.generate_uuid_token()
            
            if avoid_duplicates:
                if token not in self.generated_tokens:
                    tokens.append(token)
                    self.generated_tokens.add(token)
            else:
                tokens.append(token)
        
        return tokens
    
    def create_token_dataframe(self, tokens, repeat_count=1):
        """
        创建令牌DataFrame
        
        Args:
            tokens: 令牌列表
            repeat_count: 每个令牌重复次数
        """
        if repeat_count > 1:
            expanded_tokens = []
            for token in tokens:
                expanded_tokens.extend([token] * repeat_count)
            tokens = expanded_tokens
        
        df = pd.DataFrame({'pxvid': tokens})
        return df
    
    def save_to_excel(self, df, filename):
        """保存到Excel文件"""
        df.to_excel(filename, index=False)
        print(f"✅ 令牌表格已保存到: {filename}")
        print(f"📊 总计令牌数量: {len(df)}")
        print(f"📋 唯一令牌数量: {df['pxvid'].nunique()}")
    
    def analyze_original_tokens(self, filepath='令牌.xlsx'):
        """分析原始令牌文件的特征"""
        try:
            df = pd.read_excel(filepath)
            print("🔍 原始令牌文件分析:")
            print(f"📊 总数量: {len(df)}")
            print(f"📋 唯一数量: {df['pxvid'].nunique()}")
            print(f"🔄 平均重复次数: {len(df) / df['pxvid'].nunique():.1f}")
            
            # 分析令牌格式
            sample_tokens = df['pxvid'].head(10).tolist()
            print(f"📝 令牌示例:")
            for i, token in enumerate(sample_tokens[:5]):
                print(f"   {i+1}. {token}")
            
            # 分析格式特征
            if sample_tokens:
                parts = sample_tokens[0].split('-')
                print(f"🔧 格式分析:")
                print(f"   - 分段数: {len(parts)}")
                print(f"   - 各段长度: {[len(part) for part in parts]}")
                print(f"   - 第三段固定值: {parts[2] if len(parts) > 2 else 'N/A'}")
            
            return df
        except Exception as e:
            print(f"❌ 无法分析原始文件: {str(e)}")
            return None

def main():
    parser = argparse.ArgumentParser(description='令牌生成器 - 生成类似令牌.xlsx的表格')
    parser.add_argument('--count', type=int, default=1000, help='生成令牌数量 (默认: 1000)')
    parser.add_argument('--repeat', type=int, default=1, help='每个令牌重复次数 (默认: 1)')
    parser.add_argument('--method', choices=['uuid', 'custom', 'similar'], default='similar', 
                       help='生成方法 (默认: similar)')
    parser.add_argument('--output', type=str, default=None, help='输出文件名')
    parser.add_argument('--analyze', action='store_true', help='分析原始令牌文件')
    parser.add_argument('--base-token', type=str, default=None, help='基础令牌（用于similar方法）')
    
    args = parser.parse_args()
    
    generator = TokenGenerator()
    
    # 分析原始文件
    if args.analyze or args.method == 'similar':
        original_df = generator.analyze_original_tokens()
        if original_df is not None and args.base_token is None:
            args.base_token = original_df['pxvid'].iloc[0]  # 使用第一个令牌作为基础
    
    print("\n" + "="*60)
    print("🚀 开始生成令牌")
    print("="*60)
    
    # 生成令牌
    print(f"⚙️  生成参数:")
    print(f"   - 数量: {args.count}")
    print(f"   - 重复: {args.repeat}")
    print(f"   - 方法: {args.method}")
    if args.base_token:
        print(f"   - 基础令牌: {args.base_token}")
    
    start_time = time.time()
    tokens = generator.generate_tokens(
        count=args.count,
        method=args.method,
        base_token=args.base_token
    )
    generation_time = time.time() - start_time
    
    print(f"⏱️  生成耗时: {generation_time:.2f}秒")
    print(f"✅ 成功生成 {len(tokens)} 个唯一令牌")
    
    # 创建DataFrame
    df = generator.create_token_dataframe(tokens, args.repeat)
    
    # 生成输出文件名
    if args.output is None:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        args.output = f"generated_tokens_{timestamp}.xlsx"
    
    # 保存文件
    generator.save_to_excel(df, args.output)
    
    # 显示示例
    print(f"\n📝 生成的令牌示例:")
    for i, token in enumerate(df['pxvid'].head(5)):
        print(f"   {i+1}. {token}")
    
    print(f"\n⚠️  重要提醒:")
    print(f"   生成的令牌可能不如原始令牌.xlsx中的令牌有效")
    print(f"   原始令牌可能来自真实用户会话，具有'预验证'状态")
    print(f"   建议将生成的令牌与原始令牌混合使用以提高成功率")

if __name__ == '__main__':
    # 如果没有命令行参数，使用交互模式
    import sys
    if len(sys.argv) == 1:
        print("🎯 令牌生成器 - 交互模式")
        print("="*50)
        
        generator = TokenGenerator()
        
        # 分析原始文件
        print("📊 分析原始令牌文件...")
        original_df = generator.analyze_original_tokens()
        
        print("\n" + "="*50)
        
        # 获取用户输入
        try:
            count = int(input("🔢 请输入生成数量 (默认1000): ") or "1000")
            repeat = int(input("🔄 请输入重复次数 (默认1): ") or "1")
            
            print("\n📋 生成方法:")
            print("   1. uuid - 标准UUID格式")
            print("   2. custom - 自定义格式")
            print("   3. similar - 基于原始令牌的相似格式 (推荐)")
            
            method_choice = input("🎯 请选择方法 (1-3, 默认3): ") or "3"
            method_map = {"1": "uuid", "2": "custom", "3": "similar"}
            method = method_map.get(method_choice, "similar")
            
            base_token = None
            if method == "similar" and original_df is not None:
                base_token = original_df['pxvid'].iloc[0]
            
            # 生成令牌
            print(f"\n🚀 开始生成 {count} 个令牌...")
            start_time = time.time()
            tokens = generator.generate_tokens(count=count, method=method, base_token=base_token)
            generation_time = time.time() - start_time
            
            # 创建DataFrame
            df = generator.create_token_dataframe(tokens, repeat)
            
            # 保存文件
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"generated_tokens_{timestamp}.xlsx"
            generator.save_to_excel(df, filename)
            
            print(f"\n⏱️  生成耗时: {generation_time:.2f}秒")
            print(f"📝 令牌示例:")
            for i, token in enumerate(df['pxvid'].head(3)):
                print(f"   {i+1}. {token}")
            
            print(f"\n⚠️  重要提醒:")
            print(f"   生成的令牌可能不如原始令牌有效")
            print(f"   建议与原始令牌混合使用")
            
        except KeyboardInterrupt:
            print("\n\n👋 用户取消操作")
        except Exception as e:
            print(f"\n❌ 发生错误: {str(e)}")
    else:
        main()

# coding: utf-8
# 真实令牌生成器 - 基于成功请求模式生成真实可用的令牌
# File: real_token_generator.py

import requests
import pandas as pd
import random
import time
import queue
import threading
from datetime import datetime
from simple_proxy_crawler import SimpleProxyManager, Headers, load_pxvid_queue

class RealTokenGenerator:
    """真实令牌生成器 - 基于成功请求模式生成可用令牌"""
    
    def __init__(self):
        self.proxy_manager = SimpleProxyManager()
        self.successful_tokens = set()
        self.generated_tokens = set()
        self.lock = threading.Lock()
        self.stats = {
            'tests_performed': 0,
            'successful_tests': 0,
            'tokens_generated': 0,
            'tokens_validated': 0
        }
    
    def find_working_products(self):
        """寻找当前可用的产品ID"""
        print("🔍 寻找当前可用的产品ID...")
        
        # 一些常见的产品ID模式
        test_products = [
            "5083982424",
            "1363194395", 
            "1649269851",
            "100000001",   # 简单数字
            "200000001",
            "300000001",
            "10001",       # 短数字
            "20001",
            "30001"
        ]
        
        # 添加一些随机生成的ID
        for _ in range(10):
            # 生成8-10位的随机数字ID
            random_id = str(random.randint(10000000, 9999999999))
            test_products.append(random_id)
        
        working_products = []
        
        for product_id in test_products:
            try:
                # 不使用代理，直接测试产品是否存在
                url = f"https://www.walmart.com/ip/{product_id}"
                
                response = requests.head(url, timeout=10, verify=False)
                
                if response.status_code == 200:
                    working_products.append(product_id)
                    print(f"   ✅ 产品 {product_id} 可用")
                elif response.status_code == 404:
                    print(f"   ❌ 产品 {product_id} 不存在")
                else:
                    print(f"   ⚠️  产品 {product_id} 状态码: {response.status_code}")
                
                time.sleep(0.5)  # 避免请求过快
                
            except Exception as e:
                print(f"   ❌ 测试产品 {product_id} 失败: {str(e)}")
        
        print(f"✅ 找到 {len(working_products)} 个可用产品")
        return working_products
    
    def analyze_token_pattern(self, successful_tokens):
        """分析成功令牌的模式"""
        print(f"🔬 分析 {len(successful_tokens)} 个成功令牌的模式")
        
        if not successful_tokens:
            return None
        
        # 分析令牌格式
        sample_token = list(successful_tokens)[0]
        print(f"📋 样本令牌: {sample_token}")
        
        if '-' in sample_token and len(sample_token) == 36:
            parts = sample_token.split('-')
            print(f"📊 令牌结构: {[len(part) for part in parts]}")
            
            # 分析各段的特征
            analysis = {
                'format': 'uuid',
                'parts': parts,
                'third_segment': parts[2] if len(parts) > 2 else None,
                'timestamp_segment': parts[1] if len(parts) > 1 else None
            }
            
            # 检查第三段是否固定
            third_segments = [token.split('-')[2] for token in list(successful_tokens)[:10] if '-' in token]
            if len(set(third_segments)) == 1:
                print(f"✅ 第三段固定为: {third_segments[0]}")
                analysis['fixed_third'] = third_segments[0]
            
            # 分析第二段（可能的时间戳）
            second_segments = [token.split('-')[1] for token in list(successful_tokens)[:10] if '-' in token]
            second_values = [int(seg, 16) for seg in second_segments]
            print(f"📊 第二段十进制范围: {min(second_values)} - {max(second_values)}")
            analysis['timestamp_range'] = (min(second_values), max(second_values))
            
            return analysis
        
        return None
    
    def generate_tokens_by_pattern(self, pattern, count=100):
        """基于模式生成新令牌"""
        print(f"🧬 基于模式生成 {count} 个新令牌")
        
        if not pattern or pattern.get('format') != 'uuid':
            print("❌ 无有效模式，使用随机生成")
            return self.generate_random_tokens(count)
        
        new_tokens = []
        
        for _ in range(count):
            # 生成第一段（8位随机十六进制）
            part1 = ''.join(random.choice('0123456789abcdef') for _ in range(8))
            
            # 生成第二段（基于时间戳范围）
            if 'timestamp_range' in pattern:
                min_val, max_val = pattern['timestamp_range']
                # 在范围内生成，稍微扩展范围
                new_val = random.randint(max(0, min_val - 1000), min(0xFFFF, max_val + 1000))
                part2 = f"{new_val:04x}"
            else:
                part2 = ''.join(random.choice('0123456789abcdef') for _ in range(4))
            
            # 第三段（如果有固定值则使用，否则随机）
            if 'fixed_third' in pattern:
                part3 = pattern['fixed_third']
            else:
                part3 = ''.join(random.choice('0123456789abcdef') for _ in range(4))
            
            # 第四段和第五段（随机生成）
            part4 = ''.join(random.choice('0123456789abcdef') for _ in range(4))
            part5 = ''.join(random.choice('0123456789abcdef') for _ in range(12))
            
            new_token = f"{part1}-{part2}-{part3}-{part4}-{part5}"
            new_tokens.append(new_token)
        
        print(f"✅ 生成了 {len(new_tokens)} 个基于模式的令牌")
        return new_tokens
    
    def generate_random_tokens(self, count=100):
        """生成随机UUID格式令牌"""
        tokens = []
        for _ in range(count):
            # 生成标准UUID格式
            parts = [
                ''.join(random.choice('0123456789abcdef') for _ in range(8)),
                ''.join(random.choice('0123456789abcdef') for _ in range(4)),
                '11f0',  # 固定第三段为11f0（从原始令牌观察到的模式）
                ''.join(random.choice('0123456789abcdef') for _ in range(4)),
                ''.join(random.choice('0123456789abcdef') for _ in range(12))
            ]
            token = '-'.join(parts)
            tokens.append(token)
        
        return tokens
    
    def test_token_effectiveness(self, tokens, product_ids, sample_size=20):
        """测试令牌有效性"""
        print(f"🔍 测试令牌有效性 (样本大小: {sample_size})")
        
        if not product_ids:
            print("❌ 没有可用的产品ID进行测试")
            return 0
        
        test_tokens = random.sample(tokens, min(sample_size, len(tokens)))
        successful_count = 0
        
        for i, token in enumerate(test_tokens):
            product_id = random.choice(product_ids)
            
            try:
                proxy_config = self.proxy_manager.get_proxy()
                url = f"https://www.walmart.com/ip/{product_id}"
                cookie = {"_pxvid": token}
                
                response = requests.get(
                    url,
                    cookies=cookie,
                    headers=Headers(),
                    proxies=proxy_config,
                    timeout=15,
                    verify=False
                )
                
                with self.lock:
                    self.stats['tests_performed'] += 1
                
                # 使用宽松的成功标准
                success = (response.status_code == 200 and 
                          (response.headers.get("Accept-CH") == 'Downlink, DPR' or
                           '"productName"' in response.text or
                           len(response.text) > 50000))
                
                if success:
                    successful_count += 1
                    with self.lock:
                        self.successful_tokens.add(token)
                        self.stats['successful_tests'] += 1
                    print(f"   ✅ 令牌 {i+1}/{len(test_tokens)}: 成功")
                else:
                    print(f"   ❌ 令牌 {i+1}/{len(test_tokens)}: 失败 (状态码: {response.status_code})")
                
                time.sleep(random.uniform(1, 3))
                
            except Exception as e:
                print(f"   ❌ 令牌 {i+1}/{len(test_tokens)}: 异常 - {str(e)}")
        
        success_rate = successful_count / len(test_tokens) * 100 if test_tokens else 0
        print(f"📊 测试结果: {successful_count}/{len(test_tokens)} ({success_rate:.1f}%)")
        
        return success_rate
    
    def run_generation_cycle(self):
        """运行完整的令牌生成周期"""
        print("🎯 真实令牌生成器")
        print("="*60)
        print("基于成功请求模式生成真实可用的令牌")
        print("="*60)
        
        # 1. 寻找可用产品
        working_products = self.find_working_products()
        if not working_products:
            print("❌ 没有找到可用产品，无法继续")
            return
        
        # 2. 加载一些种子令牌进行模式分析
        try:
            vid_queue = load_pxvid_queue()
            seed_tokens = []
            for _ in range(10):
                try:
                    seed_tokens.append(vid_queue.get_nowait())
                except queue.Empty:
                    break
            print(f"🔑 加载了 {len(seed_tokens)} 个种子令牌用于模式分析")
        except Exception as e:
            print(f"⚠️  加载种子令牌失败，将使用随机生成: {str(e)}")
            seed_tokens = []
        
        # 3. 分析令牌模式
        if seed_tokens:
            pattern = self.analyze_token_pattern(seed_tokens)
        else:
            pattern = None
        
        # 4. 生成新令牌
        if pattern:
            new_tokens = self.generate_tokens_by_pattern(pattern, count=200)
        else:
            new_tokens = self.generate_random_tokens(count=200)
        
        with self.lock:
            self.generated_tokens.update(new_tokens)
            self.stats['tokens_generated'] = len(new_tokens)
        
        # 5. 测试令牌有效性
        success_rate = self.test_token_effectiveness(new_tokens, working_products, sample_size=30)
        
        # 6. 如果有成功的令牌，基于它们生成更多
        if self.successful_tokens:
            print(f"\n🧬 基于 {len(self.successful_tokens)} 个成功令牌生成更多令牌")
            successful_pattern = self.analyze_token_pattern(self.successful_tokens)
            if successful_pattern:
                additional_tokens = self.generate_tokens_by_pattern(successful_pattern, count=100)
                additional_success_rate = self.test_token_effectiveness(additional_tokens, working_products, sample_size=15)
                
                with self.lock:
                    self.generated_tokens.update(additional_tokens)
                    self.stats['tokens_generated'] += len(additional_tokens)
        
        # 7. 保存结果
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"real_tokens_{timestamp}.xlsx"
        
        # 优先保存成功的令牌
        tokens_to_save = list(self.successful_tokens) if self.successful_tokens else list(self.generated_tokens)
        
        if tokens_to_save:
            df = pd.DataFrame({'pxvid': tokens_to_save})
            df.to_excel(filename, index=False)
            print(f"💾 令牌已保存到: {filename}")
            print(f"📊 保存数量: {len(tokens_to_save)}")
        else:
            print("❌ 没有令牌可保存")
            filename = None
        
        # 8. 最终报告
        print(f"\n🎉 真实令牌生成完成!")
        print(f"📊 最终统计:")
        print(f"   - 可用产品: {len(working_products)}")
        print(f"   - 生成令牌: {self.stats['tokens_generated']}")
        print(f"   - 测试次数: {self.stats['tests_performed']}")
        print(f"   - 成功次数: {self.stats['successful_tests']}")
        print(f"   - 成功率: {self.stats['successful_tests']/max(1,self.stats['tests_performed'])*100:.1f}%")
        print(f"   - 保存文件: {filename}")
        
        if self.successful_tokens:
            print(f"\n🎉 成功生成了 {len(self.successful_tokens)} 个可用令牌!")
            print(f"💡 这些令牌基于真实的成功请求模式生成")
            print(f"💡 可以直接用于爬虫程序")
        else:
            print(f"\n⚠️  没有生成成功的令牌")
            print(f"💡 建议检查网络连接和代理设置")
        
        return filename

def main():
    generator = RealTokenGenerator()
    generator.run_generation_cycle()

if __name__ == '__main__':
    main()

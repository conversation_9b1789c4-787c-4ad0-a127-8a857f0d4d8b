# coding: utf-8
# 令牌生成器使用示例
# File: token_generator_usage.py

from token_generator import TokenGenerator
from advanced_token_generator import AdvancedTokenGenerator
import pandas as pd
import time

def demo_basic_generator():
    """演示基础令牌生成器"""
    print("🎯 基础令牌生成器演示")
    print("="*50)
    
    generator = TokenGenerator()
    
    # 分析原始文件
    print("📊 分析原始令牌文件...")
    original_df = generator.analyze_original_tokens()
    
    if original_df is not None:
        base_token = original_df['pxvid'].iloc[0]
        print(f"🔧 使用基础令牌: {base_token}")
        
        # 生成少量令牌用于测试
        print(f"\n🚀 生成 100 个测试令牌...")
        tokens = generator.generate_tokens(
            count=100,
            method='similar',
            base_token=base_token
        )
        
        # 创建DataFrame
        df = generator.create_token_dataframe(tokens, repeat_count=3)
        
        # 保存
        filename = "demo_tokens_basic.xlsx"
        generator.save_to_excel(df, filename)
        
        print(f"📝 生成的令牌示例:")
        for i, token in enumerate(df['pxvid'].head(5)):
            print(f"   {i+1}. {token}")

def demo_advanced_generator():
    """演示高级令牌生成器"""
    print("\n🎯 高级令牌生成器演示")
    print("="*50)
    
    generator = AdvancedTokenGenerator()
    
    # 获取基础令牌
    try:
        original_df = pd.read_excel('令牌.xlsx')
        base_token = original_df['pxvid'].iloc[0]
        print(f"🔧 使用基础令牌: {base_token}")
    except:
        base_token = "b4a2d149-1a61-11f0-b3eb-a7beca6a8ba1"
        print(f"⚠️  使用默认基础令牌: {base_token}")
    
    # 生成中等数量的令牌
    print(f"\n🚀 生成 2,000 个令牌 (重复 3 次)...")
    generated_file, df = generator.create_large_token_file(
        count=2000,
        repeat_count=3,
        base_token=base_token,
        output_file="demo_tokens_advanced.xlsx"
    )
    
    print(f"📝 生成的令牌示例:")
    for i, token in enumerate(df['pxvid'].head(5)):
        print(f"   {i+1}. {token}")

def test_token_format():
    """测试令牌格式兼容性"""
    print("\n🔍 令牌格式兼容性测试")
    print("="*50)
    
    generator = TokenGenerator()
    
    # 生成不同方法的令牌
    methods = ['uuid', 'custom', 'similar']
    base_token = "b4a2d149-1a61-11f0-b3eb-a7beca6a8ba1"
    
    for method in methods:
        print(f"\n📋 测试方法: {method}")
        tokens = generator.generate_tokens(
            count=5,
            method=method,
            base_token=base_token if method == 'similar' else None
        )
        
        for i, token in enumerate(tokens):
            print(f"   {i+1}. {token}")
            
            # 验证格式
            parts = token.split('-')
            if len(parts) == 5:
                lengths = [len(part) for part in parts]
                print(f"      格式: {lengths} {'✅' if lengths == [8,4,4,4,12] else '❌'}")
            else:
                print(f"      格式: 无效 ❌")

def compare_with_original():
    """与原始令牌比较"""
    print("\n📊 与原始令牌比较")
    print("="*50)
    
    try:
        # 读取原始令牌
        original_df = pd.read_excel('令牌.xlsx')
        print(f"📋 原始令牌:")
        print(f"   - 总数: {len(original_df):,}")
        print(f"   - 唯一数: {original_df['pxvid'].nunique():,}")
        
        # 生成新令牌
        generator = TokenGenerator()
        base_token = original_df['pxvid'].iloc[0]
        new_tokens = generator.generate_tokens(
            count=1000,
            method='similar',
            base_token=base_token
        )
        
        new_df = generator.create_token_dataframe(new_tokens, repeat_count=5)
        
        print(f"\n📋 生成的令牌:")
        print(f"   - 总数: {len(new_df):,}")
        print(f"   - 唯一数: {new_df['pxvid'].nunique():,}")
        
        # 检查重复
        overlap = set(original_df['pxvid']) & set(new_df['pxvid'])
        print(f"   - 与原始重复: {len(overlap)}")
        
        # 格式比较
        original_sample = original_df['pxvid'].iloc[0]
        new_sample = new_df['pxvid'].iloc[0]
        
        print(f"\n📝 格式比较:")
        print(f"   原始: {original_sample}")
        print(f"   生成: {new_sample}")
        
        # 分析第三段
        orig_parts = original_sample.split('-')
        new_parts = new_sample.split('-')
        
        if len(orig_parts) >= 3 and len(new_parts) >= 3:
            print(f"   第三段: {orig_parts[2]} vs {new_parts[2]} {'✅' if orig_parts[2] == new_parts[2] else '❌'}")
        
    except Exception as e:
        print(f"❌ 比较失败: {str(e)}")

def performance_test():
    """性能测试"""
    print("\n⚡ 性能测试")
    print("="*50)
    
    generator = AdvancedTokenGenerator()
    base_token = "b4a2d149-1a61-11f0-b3eb-a7beca6a8ba1"
    
    test_sizes = [1000, 5000, 10000]
    
    for size in test_sizes:
        print(f"\n🔢 测试 {size:,} 个令牌:")
        
        start_time = time.time()
        tokens = generator.generate_tokens_parallel(size, base_token, num_threads=4)
        generation_time = time.time() - start_time
        
        print(f"   - 生成耗时: {generation_time:.2f}秒")
        print(f"   - 生成速度: {size/generation_time:.0f} 令牌/秒")
        print(f"   - 实际生成: {len(tokens):,} 个")

def main():
    """主函数"""
    print("🎯 令牌生成器完整演示")
    print("="*60)
    
    try:
        # 1. 基础生成器演示
        demo_basic_generator()
        
        # 2. 高级生成器演示
        demo_advanced_generator()
        
        # 3. 格式测试
        test_token_format()
        
        # 4. 与原始比较
        compare_with_original()
        
        # 5. 性能测试
        performance_test()
        
        print(f"\n✅ 所有演示完成！")
        print(f"\n📋 生成的文件:")
        print(f"   - demo_tokens_basic.xlsx")
        print(f"   - demo_tokens_advanced.xlsx")
        
        print(f"\n💡 使用建议:")
        print(f"   1. 对于小批量生成，使用 token_generator.py")
        print(f"   2. 对于大批量生成，使用 advanced_token_generator.py")
        print(f"   3. 生成的令牌建议与原始令牌混合使用")
        print(f"   4. 保持第三段固定值 '11f0' 以确保格式兼容")
        
    except KeyboardInterrupt:
        print(f"\n\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {str(e)}")

if __name__ == '__main__':
    main()

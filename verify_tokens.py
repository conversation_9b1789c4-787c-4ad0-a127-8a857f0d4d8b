import pandas as pd
import os

files = [
    'demo_tokens_basic.xlsx',
    'demo_tokens_advanced.xlsx', 
    'large_tokens_5000_20250729_020313.xlsx',
    'generated_tokens_20250729_020218.xlsx'
]

print("🔍 验证生成的令牌文件")
print("="*50)

for filename in files:
    if os.path.exists(filename):
        try:
            df = pd.read_excel(filename)
            print(f"\n📁 {filename}:")
            print(f"   总数: {len(df):,}")
            print(f"   唯一: {df['pxvid'].nunique():,}")
            print(f"   示例: {df['pxvid'].iloc[0]}")
            
            # 验证格式
            sample = df['pxvid'].iloc[0]
            parts = sample.split('-')
            if len(parts) == 5 and parts[2] == '11f0':
                print(f"   格式: ✅ 正确")
            else:
                print(f"   格式: ❌ 错误")
                
        except Exception as e:
            print(f"\n❌ {filename}: 读取失败 - {str(e)}")
    else:
        print(f"\n⚠️  {filename}: 文件不存在")

print(f"\n✅ 验证完成")

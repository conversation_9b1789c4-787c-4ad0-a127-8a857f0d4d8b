# coding: utf-8
# 高级令牌生成器 - 批量生成类似令牌.xlsx的表格
# File: advanced_token_generator.py

import pandas as pd
import uuid
import random
import time
from datetime import datetime
import os
import threading
from concurrent.futures import ThreadPoolExecutor
import argparse

class AdvancedTokenGenerator:
    """高级令牌生成器"""
    
    def __init__(self):
        self.generated_tokens = set()
        self.lock = threading.Lock()
        
    def analyze_token_pattern(self, token):
        """分析令牌模式"""
        parts = token.split('-')
        if len(parts) != 5:
            return None
        
        return {
            'part1_len': len(parts[0]),  # 8位
            'part2_len': len(parts[1]),  # 4位
            'part3_fixed': parts[2],     # 固定值 11f0
            'part4_len': len(parts[3]),  # 4位
            'part5_len': len(parts[4])   # 12位
        }
    
    def generate_similar_token_batch(self, base_token, count):
        """批量生成相似令牌"""
        pattern = self.analyze_token_pattern(base_token)
        if not pattern:
            return [str(uuid.uuid4()) for _ in range(count)]
        
        tokens = []
        for _ in range(count):
            # 生成各部分
            part1 = ''.join(random.choice('0123456789abcdef') for _ in range(pattern['part1_len']))
            part2 = ''.join(random.choice('0123456789abcdef') for _ in range(pattern['part2_len']))
            part3 = pattern['part3_fixed']  # 保持固定
            part4 = ''.join(random.choice('0123456789abcdef') for _ in range(pattern['part4_len']))
            part5 = ''.join(random.choice('0123456789abcdef') for _ in range(pattern['part5_len']))
            
            token = f"{part1}-{part2}-{part3}-{part4}-{part5}"
            tokens.append(token)
        
        return tokens
    
    def generate_tokens_parallel(self, total_count, base_token, num_threads=4):
        """并行生成令牌"""
        tokens_per_thread = total_count // num_threads
        remaining = total_count % num_threads
        
        all_tokens = []
        
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = []
            
            for i in range(num_threads):
                count = tokens_per_thread + (1 if i < remaining else 0)
                if count > 0:
                    future = executor.submit(self.generate_similar_token_batch, base_token, count)
                    futures.append(future)
            
            for future in futures:
                batch_tokens = future.result()
                all_tokens.extend(batch_tokens)
        
        return all_tokens
    
    def create_large_token_file(self, count, repeat_count=5, base_token=None, output_file=None):
        """创建大型令牌文件"""
        print(f"🚀 开始生成 {count:,} 个令牌 (重复 {repeat_count} 次)")
        
        start_time = time.time()
        
        # 并行生成令牌
        tokens = self.generate_tokens_parallel(count, base_token, num_threads=8)
        
        generation_time = time.time() - start_time
        print(f"⏱️  令牌生成耗时: {generation_time:.2f}秒")
        
        # 创建重复令牌
        if repeat_count > 1:
            print(f"🔄 创建重复令牌 (每个重复 {repeat_count} 次)...")
            expanded_tokens = []
            for token in tokens:
                expanded_tokens.extend([token] * repeat_count)
            tokens = expanded_tokens
        
        # 创建DataFrame
        print(f"📊 创建DataFrame...")
        df = pd.DataFrame({'pxvid': tokens})
        
        # 保存文件
        if output_file is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f"large_tokens_{count}_{timestamp}.xlsx"
        
        print(f"💾 保存到文件: {output_file}")
        save_start = time.time()
        df.to_excel(output_file, index=False)
        save_time = time.time() - save_start
        
        total_time = time.time() - start_time
        
        print(f"✅ 完成！")
        print(f"📊 统计信息:")
        print(f"   - 总令牌数: {len(df):,}")
        print(f"   - 唯一令牌数: {df['pxvid'].nunique():,}")
        print(f"   - 文件大小: {os.path.getsize(output_file) / 1024 / 1024:.2f} MB")
        print(f"   - 生成耗时: {generation_time:.2f}秒")
        print(f"   - 保存耗时: {save_time:.2f}秒")
        print(f"   - 总耗时: {total_time:.2f}秒")
        
        return output_file, df
    
    def merge_with_original(self, generated_file, original_file='令牌.xlsx', output_file=None):
        """将生成的令牌与原始令牌合并"""
        print(f"🔄 合并令牌文件...")
        
        try:
            # 读取文件
            generated_df = pd.read_excel(generated_file)
            original_df = pd.read_excel(original_file)
            
            print(f"📊 文件信息:")
            print(f"   - 生成的令牌: {len(generated_df):,}")
            print(f"   - 原始令牌: {len(original_df):,}")
            
            # 合并
            merged_df = pd.concat([original_df, generated_df], ignore_index=True)
            
            # 打乱顺序
            merged_df = merged_df.sample(frac=1).reset_index(drop=True)
            
            if output_file is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                output_file = f"merged_tokens_{timestamp}.xlsx"
            
            merged_df.to_excel(output_file, index=False)
            
            print(f"✅ 合并完成:")
            print(f"   - 合并后总数: {len(merged_df):,}")
            print(f"   - 唯一令牌数: {merged_df['pxvid'].nunique():,}")
            print(f"   - 保存到: {output_file}")
            
            return output_file, merged_df
            
        except Exception as e:
            print(f"❌ 合并失败: {str(e)}")
            return None, None

def main():
    parser = argparse.ArgumentParser(description='高级令牌生成器')
    parser.add_argument('--count', type=int, default=50000, help='生成令牌数量')
    parser.add_argument('--repeat', type=int, default=5, help='每个令牌重复次数')
    parser.add_argument('--output', type=str, help='输出文件名')
    parser.add_argument('--merge', action='store_true', help='与原始令牌合并')
    parser.add_argument('--base-token', type=str, help='基础令牌')
    
    args = parser.parse_args()
    
    generator = AdvancedTokenGenerator()
    
    # 获取基础令牌
    if args.base_token is None:
        try:
            original_df = pd.read_excel('令牌.xlsx')
            args.base_token = original_df['pxvid'].iloc[0]
            print(f"🔧 使用原始文件中的基础令牌: {args.base_token}")
        except:
            args.base_token = "b4a2d149-1a61-11f0-b3eb-a7beca6a8ba1"
            print(f"⚠️  使用默认基础令牌: {args.base_token}")
    
    # 生成令牌文件
    generated_file, df = generator.create_large_token_file(
        count=args.count,
        repeat_count=args.repeat,
        base_token=args.base_token,
        output_file=args.output
    )
    
    # 显示示例
    print(f"\n📝 生成的令牌示例:")
    for i, token in enumerate(df['pxvid'].head(5)):
        print(f"   {i+1}. {token}")
    
    # 合并选项
    if args.merge:
        print(f"\n" + "="*60)
        merged_file, merged_df = generator.merge_with_original(generated_file)
        if merged_df is not None:
            print(f"\n📝 合并后令牌示例:")
            for i, token in enumerate(merged_df['pxvid'].head(5)):
                print(f"   {i+1}. {token}")

if __name__ == '__main__':
    # 交互模式
    import sys
    if len(sys.argv) == 1:
        print("🎯 高级令牌生成器 - 交互模式")
        print("="*60)
        
        generator = AdvancedTokenGenerator()
        
        try:
            # 获取基础令牌
            try:
                original_df = pd.read_excel('令牌.xlsx')
                base_token = original_df['pxvid'].iloc[0]
                print(f"🔧 检测到原始令牌文件，使用基础令牌: {base_token}")
            except:
                base_token = "b4a2d149-1a61-11f0-b3eb-a7beca6a8ba1"
                print(f"⚠️  使用默认基础令牌: {base_token}")
            
            print(f"\n📋 预设方案:")
            print(f"   1. 小批量 (1,000 令牌 × 5 重复 = 5,000)")
            print(f"   2. 中批量 (10,000 令牌 × 5 重复 = 50,000)")
            print(f"   3. 大批量 (50,000 令牌 × 5 重复 = 250,000)")
            print(f"   4. 超大批量 (100,000 令牌 × 5 重复 = 500,000)")
            print(f"   5. 自定义")
            
            choice = input("🎯 请选择方案 (1-5): ") or "2"
            
            if choice == "1":
                count, repeat = 1000, 5
            elif choice == "2":
                count, repeat = 10000, 5
            elif choice == "3":
                count, repeat = 50000, 5
            elif choice == "4":
                count, repeat = 100000, 5
            else:
                count = int(input("🔢 请输入生成数量: ") or "10000")
                repeat = int(input("🔄 请输入重复次数: ") or "5")
            
            print(f"\n🚀 开始生成 {count:,} × {repeat} = {count * repeat:,} 个令牌...")
            
            # 生成
            generated_file, df = generator.create_large_token_file(
                count=count,
                repeat_count=repeat,
                base_token=base_token
            )
            
            # 询问是否合并
            merge_choice = input(f"\n🔄 是否与原始令牌合并？(y/N): ").lower()
            if merge_choice in ['y', 'yes']:
                merged_file, merged_df = generator.merge_with_original(generated_file)
            
            print(f"\n✅ 全部完成！")
            
        except KeyboardInterrupt:
            print(f"\n\n👋 用户取消操作")
        except Exception as e:
            print(f"\n❌ 发生错误: {str(e)}")
    else:
        main()

# coding: utf-8
# 智能令牌收集器 - 专门收集真正的PerimeterX令牌
# File: smart_token_collector.py

import requests
import pandas as pd
import random
import time
import re
import queue
import threading
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
from simple_proxy_crawler import <PERSON>ProxyMana<PERSON>, Headers, load_pxvid_queue

class SmartTokenCollector:
    """智能令牌收集器 - 专门收集真正的PerimeterX令牌"""
    
    def __init__(self):
        self.proxy_manager = SimpleProxyManager()
        self.collected_tokens = set()
        self.verified_tokens = set()
        self.lock = threading.Lock()
        self.stats = {
            'requests_made': 0,
            'successful_requests': 0,
            'tokens_collected': 0,
            'tokens_verified': 0
        }
    
    def extract_perimeter_tokens(self, response):
        """专门提取PerimeterX相关的令牌"""
        tokens = set()
        
        # 1. 从Set-Cookie头中提取
        set_cookie_header = response.headers.get('Set-Cookie', '')
        if set_cookie_header:
            # 提取_pxvid
            pxvid_match = re.search(r'_pxvid=([^;]+)', set_cookie_header)
            if pxvid_match:
                tokens.add(pxvid_match.group(1))
            
            # 提取_pxhd并解析
            pxhd_match = re.search(r'_pxhd=([^;]+)', set_cookie_header)
            if pxhd_match:
                pxhd_value = pxhd_match.group(1)
                if ':' in pxhd_value:
                    extracted_token = pxhd_value.split(':')[-1]
                    tokens.add(extracted_token)
                else:
                    tokens.add(pxhd_value)
            
            # 提取_px3
            px3_match = re.search(r'_px3=([^;]+)', set_cookie_header)
            if px3_match:
                tokens.add(px3_match.group(1))
        
        # 2. 从响应cookies中提取
        for cookie_name in ['_pxvid', '_pxhd', '_px3']:
            if cookie_name in response.cookies:
                cookie_value = response.cookies[cookie_name]
                if cookie_name == '_pxhd' and ':' in cookie_value:
                    extracted_token = cookie_value.split(':')[-1]
                    tokens.add(extracted_token)
                else:
                    tokens.add(cookie_value)
        
        # 3. 从响应体中寻找PerimeterX相关的令牌
        html = response.text
        
        # 寻找类似原始令牌格式的令牌（第三段为11f0）
        original_pattern = r'[0-9a-f]{8}-[0-9a-f]{4}-11f0-[0-9a-f]{4}-[0-9a-f]{12}'
        found_original_format = re.findall(original_pattern, html)
        tokens.update(found_original_format)
        
        # 寻找PerimeterX相关的JavaScript变量
        px_patterns = [
            r'_pxvid["\']?\s*[:=]\s*["\']([^"\'>\s,}]+)',
            r'_pxhd["\']?\s*[:=]\s*["\']([^"\'>\s,}]+)',
            r'pxvid["\']?\s*[:=]\s*["\']([^"\'>\s,}]+)',
            r'window\._pxvid\s*=\s*["\']([^"\']+)',
            r'PX_.*?["\']([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})'
        ]
        
        for pattern in px_patterns:
            matches = re.findall(pattern, html, re.IGNORECASE)
            for match in matches:
                # 如果是_pxhd格式，提取最后部分
                if ':' in match:
                    extracted = match.split(':')[-1]
                    if len(extracted) >= 30:  # 合理的令牌长度
                        tokens.add(extracted)
                else:
                    tokens.add(match)
        
        # 过滤掉明显不是令牌的值
        filtered_tokens = set()
        for token in tokens:
            if token and len(token) >= 30 and not token.startswith('http'):
                # 检查是否是UUID格式或类似格式
                if re.match(r'^[0-9a-f-]{36}$', token) or re.match(r'^[0-9a-f]{32,}$', token):
                    filtered_tokens.add(token)
        
        return list(filtered_tokens)
    
    def collect_tokens_with_session_evolution(self, product_ids, seed_tokens, max_requests=50):
        """通过会话演进收集令牌"""
        print(f"🔄 开始会话演进令牌收集")
        print(f"📊 产品数量: {len(product_ids)}")
        print(f"🔑 种子令牌数量: {len(seed_tokens)}")
        
        session = requests.Session()
        current_tokens = seed_tokens.copy()
        
        for i in range(max_requests):
            if not current_tokens:
                print("❌ 没有可用令牌，停止收集")
                break
            
            # 选择一个令牌和产品
            current_token = random.choice(current_tokens)
            product_id = random.choice(product_ids)
            
            try:
                proxy_config = self.proxy_manager.get_proxy()
                url = f"https://www.walmart.com/ip/{product_id}"
                
                # 设置完整的cookie
                cookies = {
                    "_pxvid": current_token,
                    "_pxhd": f"some_prefix:{current_token}",  # 模拟完整的_pxhd格式
                }
                
                print(f"🔄 请求 {i+1}/{max_requests}: 产品 {product_id}, 令牌 {current_token[:15]}...")
                
                response = session.get(
                    url,
                    cookies=cookies,
                    headers=Headers(),
                    proxies=proxy_config,
                    timeout=15,
                    verify=False
                )
                
                with self.lock:
                    self.stats['requests_made'] += 1
                
                # 提取新令牌
                new_tokens = self.extract_perimeter_tokens(response)
                
                if new_tokens:
                    print(f"   ✅ 发现 {len(new_tokens)} 个新令牌")
                    for token in new_tokens:
                        print(f"      - {token[:15]}...")
                        with self.lock:
                            if token not in self.collected_tokens:
                                self.collected_tokens.add(token)
                                current_tokens.append(token)  # 添加到当前令牌池
                                self.stats['tokens_collected'] += 1
                else:
                    print(f"   ❌ 未发现新令牌")
                
                # 检查是否成功
                if response.status_code == 200:
                    with self.lock:
                        self.stats['successful_requests'] += 1
                
                time.sleep(random.uniform(1, 3))  # 随机延时
                
            except Exception as e:
                print(f"   ❌ 请求失败: {str(e)}")
        
        print(f"\n📊 会话演进收集完成:")
        print(f"   - 请求总数: {self.stats['requests_made']}")
        print(f"   - 成功请求: {self.stats['successful_requests']}")
        print(f"   - 收集令牌: {self.stats['tokens_collected']}")
    
    def generate_similar_tokens(self, base_tokens, count=100):
        """基于成功令牌生成相似令牌"""
        print(f"🧬 基于 {len(base_tokens)} 个基础令牌生成相似令牌")
        
        generated_tokens = set()
        
        for _ in range(count):
            if not base_tokens:
                break
            
            base_token = random.choice(list(base_tokens))
            
            # 如果是标准UUID格式
            if '-' in base_token and len(base_token) == 36:
                parts = base_token.split('-')
                if len(parts) == 5:
                    # 保持第三段不变（如果是11f0），随机化其他段
                    new_parts = []
                    for i, part in enumerate(parts):
                        if i == 2 and part == '11f0':
                            new_parts.append(part)  # 保持11f0不变
                        else:
                            # 生成相似但不同的值
                            new_part = ''.join(random.choice('0123456789abcdef') for _ in range(len(part)))
                            new_parts.append(new_part)
                    
                    new_token = '-'.join(new_parts)
                    generated_tokens.add(new_token)
            else:
                # 对于其他格式的令牌，生成相似长度的随机令牌
                new_token = ''.join(random.choice('0123456789abcdef') for _ in range(len(base_token)))
                generated_tokens.add(new_token)
        
        print(f"✅ 生成了 {len(generated_tokens)} 个相似令牌")
        return list(generated_tokens)
    
    def verify_token_batch(self, tokens, sample_size=20):
        """批量验证令牌"""
        print(f"🔍 批量验证令牌 (样本大小: {sample_size})")
        
        tokens_to_verify = random.sample(list(tokens), min(sample_size, len(tokens)))
        verified_count = 0
        
        for i, token in enumerate(tokens_to_verify):
            try:
                proxy_config = self.proxy_manager.get_proxy()
                url = "https://www.walmart.com/ip/5083982424"  # 使用固定的测试产品
                cookie = {"_pxvid": token}
                
                response = requests.get(
                    url,
                    cookies=cookie,
                    headers=Headers(),
                    proxies=proxy_config,
                    timeout=10,
                    verify=False
                )
                
                # 使用宽松的成功标准
                success = (response.status_code == 200 and 
                          (response.headers.get("Accept-CH") == 'Downlink, DPR' or 
                           '"productName"' in response.text or
                           len(response.text) > 100000))  # 大页面通常是成功的
                
                if success:
                    with self.lock:
                        self.verified_tokens.add(token)
                        verified_count += 1
                    print(f"   ✅ 令牌 {i+1}/{len(tokens_to_verify)}: 验证成功")
                else:
                    print(f"   ❌ 令牌 {i+1}/{len(tokens_to_verify)}: 验证失败")
                
                time.sleep(0.5)  # 短暂延时
                
            except Exception as e:
                print(f"   ❌ 令牌 {i+1}/{len(tokens_to_verify)}: 验证异常 - {str(e)}")
        
        verification_rate = verified_count / len(tokens_to_verify) * 100 if tokens_to_verify else 0
        
        with self.lock:
            self.stats['tokens_verified'] = len(self.verified_tokens)
        
        print(f"📊 验证结果: {verified_count}/{len(tokens_to_verify)} ({verification_rate:.1f}%)")
        return verification_rate
    
    def run_smart_collection(self):
        """运行智能令牌收集"""
        print("🧠 智能令牌收集器")
        print("="*60)
        
        # 1. 加载种子令牌
        try:
            vid_queue = load_pxvid_queue()
            seed_tokens = []
            for _ in range(20):  # 使用20个种子令牌
                try:
                    seed_tokens.append(vid_queue.get_nowait())
                except queue.Empty:
                    break
            print(f"🔑 加载了 {len(seed_tokens)} 个种子令牌")
        except Exception as e:
            print(f"❌ 加载种子令牌失败: {str(e)}")
            return
        
        # 2. 生成测试产品ID
        product_ids = ["5083982424", "1363194395", "1649269851"]
        for _ in range(20):
            product_ids.append(str(random.randint(1000000000, 9999999999)))
        
        # 3. 会话演进收集
        self.collect_tokens_with_session_evolution(product_ids, seed_tokens, max_requests=30)
        
        # 4. 如果收集到令牌，生成相似令牌
        if self.collected_tokens:
            similar_tokens = self.generate_similar_tokens(self.collected_tokens, count=50)
            self.collected_tokens.update(similar_tokens)
            self.stats['tokens_collected'] += len(similar_tokens)
        
        # 5. 验证令牌
        if self.collected_tokens:
            verification_rate = self.verify_token_batch(self.collected_tokens, sample_size=15)
        else:
            verification_rate = 0
            print("❌ 没有收集到令牌")
        
        # 6. 保存结果
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"smart_tokens_{timestamp}.xlsx"
        
        tokens_to_save = list(self.verified_tokens) if self.verified_tokens else list(self.collected_tokens)
        
        if tokens_to_save:
            df = pd.DataFrame({'pxvid': tokens_to_save})
            df.to_excel(filename, index=False)
            print(f"💾 令牌已保存到: {filename}")
        else:
            print("❌ 没有令牌可保存")
            filename = None
        
        # 7. 最终统计
        print(f"\n🎉 智能收集完成!")
        print(f"📊 最终统计:")
        print(f"   - 请求总数: {self.stats['requests_made']}")
        print(f"   - 成功请求: {self.stats['successful_requests']}")
        print(f"   - 收集令牌: {self.stats['tokens_collected']}")
        print(f"   - 验证令牌: {self.stats['tokens_verified']}")
        print(f"   - 验证成功率: {verification_rate:.1f}%")
        print(f"   - 保存文件: {filename}")
        
        return filename

def main():
    collector = SmartTokenCollector()
    collector.run_smart_collection()

if __name__ == '__main__':
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
响应内容和令牌使用打印工具
参考 gui_final_brand_scraper.py 的请求处理逻辑
"""

import requests
import json
import time
import random
import urllib.parse
import logging
from datetime import datetime
import brotli
import gzip
from bs4 import BeautifulSoup

# 尝试导入curl_cffi用于TLS指纹伪造
try:
    import curl_cffi.requests as cf_requests
    CURL_CFFI_AVAILABLE = True
    print("✅ curl_cffi可用 - 将使用TLS指纹伪造")
except ImportError:
    CURL_CFFI_AVAILABLE = False
    print("⚠️ curl_cffi不可用 - 将使用标准requests")

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('response_token_printer.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ResponseTokenPrinter:
    """响应内容和令牌使用打印器"""
    
    def __init__(self):
        # 浏览器配置文件（包含各种令牌信息）
        self.browser_profiles = [
            {
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'sec_ch_ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                'sec_ch_ua_platform': '"Windows"',
                'accept_language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
                'profile_name': 'Chrome 120 Windows'
            },
            {
                'user_agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'sec_ch_ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                'sec_ch_ua_platform': '"macOS"',
                'accept_language': 'en-US,en;q=0.9',
                'profile_name': 'Chrome 120 macOS'
            }
        ]
        
        # API令牌示例（实际使用时需要替换为真实令牌）
        self.api_tokens = {
            'authorization': 'Bearer your-api-token-here',
            'x-api-key': 'your-api-key-here',
            'x-client-id': 'your-client-id-here'
        }
    
    def get_advanced_headers(self, url, include_api_tokens=False):
        """生成高级反检测请求头（包含各种令牌）"""
        profile = random.choice(self.browser_profiles)
        
        headers = {
            'User-Agent': profile['user_agent'],
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': profile['accept_language'],
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'sec-ch-ua': profile['sec_ch_ua'],
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': profile['sec_ch_ua_platform'],
            'Cache-Control': 'max-age=0',
        }
        
        # 添加API令牌（如果需要）
        if include_api_tokens:
            headers.update(self.api_tokens)
        
        # 添加Referer（如果不是首次访问）
        if 'trademarkia.com' in url:
            headers['Referer'] = 'https://www.trademarkia.com/'
        elif 'api' in url.lower():
            headers['Content-Type'] = 'application/json'
        
        return headers, profile
    
    def print_request_info(self, url, headers, profile):
        """打印请求信息和令牌使用情况"""
        print("\n" + "="*80)
        print("📤 REQUEST INFORMATION")
        print("="*80)
        print(f"🌐 URL: {url}")
        print(f"🖥️  Browser Profile: {profile['profile_name']}")
        print(f"⏰ Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        print("\n📋 REQUEST HEADERS (包含令牌信息):")
        print("-" * 50)
        for key, value in headers.items():
            # 高亮显示重要的令牌和认证信息
            if any(token_key in key.lower() for token_key in ['authorization', 'token', 'key', 'auth', 'bearer']):
                print(f"🔑 {key}: {value}")
            elif key.lower() in ['user-agent', 'sec-ch-ua']:
                print(f"🤖 {key}: {value}")
            else:
                print(f"   {key}: {value}")
    
    def decompress_response(self, response, is_cffi=False):
        """智能解压响应内容"""
        try:
            # curl_cffi通常自动处理解压，直接使用text
            if is_cffi:
                return response.text
            
            # 对于标准requests，先尝试直接使用text
            try:
                text_content = response.text
                # 简单检查内容是否看起来像HTML/文本
                if '<html' in text_content.lower() or len(text_content) > 0:
                    return text_content
            except:
                pass
            
            # 如果直接使用text失败，再尝试手动解压
            content_encoding = response.headers.get('content-encoding', '').lower()
            
            if content_encoding == 'br':
                try:
                    return brotli.decompress(response.content).decode('utf-8')
                except Exception as e:
                    logger.debug(f"Brotli解压失败，使用原始内容: {str(e)}")
                    return response.text
            elif content_encoding == 'gzip':
                try:
                    return gzip.decompress(response.content).decode('utf-8')
                except Exception as e:
                    logger.debug(f"Gzip解压失败，使用原始内容: {str(e)}")
                    return response.text
            else:
                return response.text
                
        except Exception as e:
            logger.debug(f"内容处理失败，使用备用方案: {str(e)}")
            return response.text if hasattr(response, 'text') else str(response.content)
    
    def print_response_info(self, response, content, is_cffi=False):
        """打印响应信息和内容"""
        print("\n" + "="*80)
        print("📥 RESPONSE INFORMATION")
        print("="*80)
        print(f"📊 Status Code: {response.status_code}")
        print(f"🔧 Request Method: {'curl_cffi' if is_cffi else 'requests'}")
        print(f"📏 Content Length: {len(content)} characters")
        print(f"🗜️  Content Encoding: {response.headers.get('content-encoding', 'None')}")
        print(f"📄 Content Type: {response.headers.get('content-type', 'Unknown')}")
        
        print("\n📋 RESPONSE HEADERS:")
        print("-" * 50)
        for key, value in response.headers.items():
            # 高亮显示重要的响应头
            if any(important in key.lower() for important in ['content-type', 'content-length', 'server', 'set-cookie']):
                print(f"⭐ {key}: {value}")
            else:
                print(f"   {key}: {value}")
        
        # 分析内容类型并打印相应信息
        content_type = response.headers.get('content-type', '').lower()
        
        if 'json' in content_type:
            self.print_json_content(content)
        elif 'html' in content_type:
            self.print_html_content(content)
        else:
            self.print_text_content(content)
    
    def print_json_content(self, content):
        """打印JSON响应内容"""
        print("\n📄 JSON RESPONSE CONTENT:")
        print("-" * 50)
        try:
            json_data = json.loads(content)
            print(json.dumps(json_data, indent=2, ensure_ascii=False))
        except json.JSONDecodeError:
            print("⚠️ 无法解析JSON内容，显示原始文本:")
            print(content[:1000] + "..." if len(content) > 1000 else content)
    
    def print_html_content(self, content):
        """打印HTML响应内容（提取关键信息）"""
        print("\n📄 HTML RESPONSE CONTENT:")
        print("-" * 50)
        try:
            soup = BeautifulSoup(content, 'html.parser')
            
            # 提取标题
            title = soup.find('title')
            if title:
                print(f"📰 Page Title: {title.get_text().strip()}")
            
            # 提取meta信息
            meta_description = soup.find('meta', attrs={'name': 'description'})
            if meta_description:
                print(f"📝 Description: {meta_description.get('content', '')}")
            
            # 显示部分HTML内容
            print(f"\n📄 HTML Content Preview (前500字符):")
            print(content[:500] + "..." if len(content) > 500 else content)
            
        except Exception as e:
            print(f"⚠️ HTML解析失败: {str(e)}")
            print(f"📄 Raw Content Preview (前500字符):")
            print(content[:500] + "..." if len(content) > 500 else content)
    
    def print_text_content(self, content):
        """打印文本响应内容"""
        print("\n📄 TEXT RESPONSE CONTENT:")
        print("-" * 50)
        print(f"📄 Content Preview (前1000字符):")
        print(content[:1000] + "..." if len(content) > 1000 else content)
    
    def make_request_and_print(self, url, include_api_tokens=False, use_cffi=True):
        """发送请求并打印详细信息"""
        try:
            # 生成请求头和配置文件信息
            headers, profile = self.get_advanced_headers(url, include_api_tokens)
            
            # 打印请求信息
            self.print_request_info(url, headers, profile)
            
            # 随机延迟
            delay = random.uniform(1, 3)
            print(f"\n⏱️  Random Delay: {delay:.2f} seconds")
            time.sleep(delay)
            
            response = None
            content = None
            is_cffi = False
            
            # 优先使用curl_cffi
            if CURL_CFFI_AVAILABLE and use_cffi:
                try:
                    print("\n🚀 Sending request with curl_cffi (TLS fingerprint spoofing)...")
                    response = cf_requests.get(
                        url,
                        headers=headers,
                        timeout=30,
                        impersonate="chrome120",
                        verify=False
                    )
                    is_cffi = True
                    content = self.decompress_response(response, is_cffi=True)
                except Exception as e:
                    print(f"⚠️ curl_cffi请求失败: {str(e)}")
                    response = None
            
            # 备用：使用标准requests
            if not response:
                print("\n🚀 Sending request with standard requests...")
                session = requests.Session()
                response = session.get(url, headers=headers, timeout=30)
                content = self.decompress_response(response, is_cffi=False)
            
            # 打印响应信息
            if response and response.status_code == 200:
                self.print_response_info(response, content, is_cffi)
                
                # 检查是否被Cloudflare拦截
                if 'cloudflare' in content.lower() and 'checking your browser' in content.lower():
                    print("\n🛡️ ⚠️ 检测到Cloudflare保护页面!")
                    print("建议使用不同的请求头或延长延迟时间")
                
                return True
            else:
                print(f"\n❌ Request failed with status code: {response.status_code if response else 'No response'}")
                return False
                
        except Exception as e:
            print(f"\n❌ Request failed with exception: {str(e)}")
            logger.error(f"请求失败: {str(e)}")
            return False

def main():
    """主函数 - 演示响应内容和令牌使用"""
    printer = ResponseTokenPrinter()
    
    print("🎯 响应内容和令牌使用打印工具")
    print("参考 gui_final_brand_scraper.py 的请求处理逻辑")
    
    # 示例URL列表
    test_urls = [
        "https://httpbin.org/headers",  # 显示请求头信息
        "https://httpbin.org/json",     # 返回JSON响应
        "https://www.trademarkia.com/search/trademarks?query=apple&reset_page=true&country=us",  # 商标搜索示例
    ]
    
    for i, url in enumerate(test_urls, 1):
        print(f"\n{'='*100}")
        print(f"🧪 TEST {i}: {url}")
        print('='*100)
        
        # 第一个URL包含API令牌演示
        include_tokens = (i == 1)
        
        success = printer.make_request_and_print(url, include_api_tokens=include_tokens)
        
        if not success:
            print(f"❌ Test {i} failed")
        else:
            print(f"✅ Test {i} completed successfully")
        
        # 测试间隔
        if i < len(test_urls):
            print(f"\n⏳ Waiting 3 seconds before next test...")
            time.sleep(3)
    
    print(f"\n🎉 All tests completed!")

if __name__ == "__main__":
    main()

# coding: utf-8
# 验证令牌收集器 - 通过真实请求收集经过验证的令牌
# File: verified_token_collector.py

import requests
import pandas as pd
import random
import time
import re
import queue
import threading
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
from simple_proxy_crawler import SimpleProxyManager, Headers, load_pxvid_queue

class VerifiedTokenCollector:
    """验证令牌收集器 - 通过成功请求收集新令牌"""
    
    def __init__(self):
        self.proxy_manager = SimpleProxyManager()
        self.collected_tokens = set()
        self.verified_tokens = set()
        self.lock = threading.Lock()
        self.stats = {
            'requests_made': 0,
            'successful_requests': 0,
            'tokens_collected': 0,
            'tokens_verified': 0
        }
    
    def extract_tokens_from_response(self, response_text):
        """从响应中提取所有可能的令牌"""
        tokens = set()
        
        # 1. 提取UUID格式的令牌
        uuid_pattern = r'[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
        found_uuids = re.findall(uuid_pattern, response_text)
        tokens.update(found_uuids)
        
        # 2. 提取类似原始令牌格式的令牌（第三段为11f0）
        original_pattern = r'[0-9a-f]{8}-[0-9a-f]{4}-11f0-[0-9a-f]{4}-[0-9a-f]{12}'
        found_original_format = re.findall(original_pattern, response_text)
        tokens.update(found_original_format)
        
        return list(tokens)
    
    def verify_token(self, token, test_product_id="5083982424"):
        """验证令牌是否有效"""
        try:
            proxy_config = self.proxy_manager.get_proxy()
            url = f"https://www.walmart.com/ip/{test_product_id}"
            cookie = {"_pxvid": token}
            
            response = requests.get(
                url,
                cookies=cookie,
                headers=Headers(),
                proxies=proxy_config,
                timeout=10,
                verify=False
            )
            
            # 判断是否成功（使用宽松的标准）
            success = (response.status_code == 200 and 
                      (response.headers.get("Accept-CH") == 'Downlink, DPR' or 
                       '"productName"' in response.text))
            
            return success, response
            
        except Exception as e:
            return False, None
    
    def collect_tokens_from_product(self, product_id, seed_token):
        """从单个产品页面收集令牌"""
        try:
            proxy_config = self.proxy_manager.get_proxy()
            url = f"https://www.walmart.com/ip/{product_id}"
            cookie = {"_pxvid": seed_token}
            
            with self.lock:
                self.stats['requests_made'] += 1
            
            response = requests.get(
                url,
                cookies=cookie,
                headers=Headers(),
                proxies=proxy_config,
                timeout=15,
                verify=False
            )
            
            # 检查请求是否成功
            success = response.status_code == 200
            if success:
                with self.lock:
                    self.stats['successful_requests'] += 1
            
            # 无论是否"成功"，都尝试提取令牌
            new_tokens = self.extract_tokens_from_response(response.text)
            
            collected_count = 0
            for token in new_tokens:
                with self.lock:
                    if token not in self.collected_tokens:
                        self.collected_tokens.add(token)
                        collected_count += 1
            
            with self.lock:
                self.stats['tokens_collected'] += collected_count
            
            print(f"📦 产品 {product_id}: 状态码 {response.status_code}, 收集 {collected_count} 个新令牌 (代理: {proxy_config['info']})")
            
            return new_tokens
            
        except Exception as e:
            print(f"❌ 产品 {product_id} 请求失败: {str(e)}")
            return []
    
    def batch_collect_tokens(self, product_ids, seed_tokens, max_workers=10):
        """批量收集令牌"""
        print(f"🚀 开始批量收集令牌")
        print(f"📊 产品数量: {len(product_ids)}")
        print(f"🔑 种子令牌数量: {len(seed_tokens)}")
        print(f"🔄 并发数: {max_workers}")
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = []
            
            for i, product_id in enumerate(product_ids):
                # 循环使用种子令牌
                seed_token = seed_tokens[i % len(seed_tokens)]
                future = executor.submit(self.collect_tokens_from_product, product_id, seed_token)
                futures.append(future)
                
                # 添加延时避免过于激进
                time.sleep(0.1)
            
            # 等待所有任务完成
            for future in futures:
                try:
                    future.result(timeout=30)
                except Exception as e:
                    print(f"❌ 任务执行失败: {str(e)}")
        
        elapsed_time = time.time() - start_time
        
        print(f"\n📊 收集完成统计:")
        print(f"   - 耗时: {elapsed_time:.2f}秒")
        print(f"   - 请求总数: {self.stats['requests_made']}")
        print(f"   - 成功请求: {self.stats['successful_requests']}")
        print(f"   - 收集令牌: {self.stats['tokens_collected']}")
        print(f"   - 收集速度: {self.stats['tokens_collected']/elapsed_time:.1f} 令牌/秒")
    
    def verify_collected_tokens(self, sample_size=50):
        """验证收集到的令牌"""
        print(f"\n🔍 开始验证收集到的令牌")
        
        # 随机选择一些令牌进行验证
        tokens_to_verify = random.sample(list(self.collected_tokens), 
                                       min(sample_size, len(self.collected_tokens)))
        
        print(f"📊 验证样本数量: {len(tokens_to_verify)}")
        
        verified_count = 0
        for i, token in enumerate(tokens_to_verify):
            print(f"🔍 验证令牌 {i+1}/{len(tokens_to_verify)}: {token[:15]}...")
            
            success, response = self.verify_token(token)
            if success:
                with self.lock:
                    self.verified_tokens.add(token)
                    verified_count += 1
                print(f"   ✅ 验证成功")
            else:
                print(f"   ❌ 验证失败")
            
            time.sleep(1)  # 避免验证过快
        
        with self.lock:
            self.stats['tokens_verified'] = len(self.verified_tokens)
        
        verification_rate = verified_count / len(tokens_to_verify) * 100
        print(f"\n📊 验证结果:")
        print(f"   - 验证成功: {verified_count}/{len(tokens_to_verify)}")
        print(f"   - 成功率: {verification_rate:.1f}%")
        
        return verification_rate
    
    def save_tokens_to_excel(self, filename=None):
        """保存令牌到Excel文件"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"verified_tokens_{timestamp}.xlsx"
        
        # 优先保存验证过的令牌，如果没有则保存所有收集的令牌
        tokens_to_save = list(self.verified_tokens) if self.verified_tokens else list(self.collected_tokens)
        
        if not tokens_to_save:
            print("❌ 没有令牌可保存")
            return None
        
        # 创建DataFrame
        df = pd.DataFrame({'pxvid': tokens_to_save})
        
        # 保存到Excel
        df.to_excel(filename, index=False)
        
        print(f"💾 令牌已保存到: {filename}")
        print(f"📊 保存数量: {len(tokens_to_save)}")
        
        return filename
    
    def run_collection_cycle(self, target_token_count=1000, max_products=200):
        """运行完整的令牌收集周期"""
        print("🎯 开始验证令牌收集周期")
        print("="*60)
        
        # 1. 加载种子令牌
        print("🔑 加载种子令牌...")
        try:
            vid_queue = load_pxvid_queue()
            seed_tokens = []
            for _ in range(min(50, vid_queue.qsize())):  # 使用50个种子令牌
                try:
                    seed_tokens.append(vid_queue.get_nowait())
                except queue.Empty:
                    break
            
            print(f"✅ 加载了 {len(seed_tokens)} 个种子令牌")
        except Exception as e:
            print(f"❌ 加载种子令牌失败: {str(e)}")
            return
        
        # 2. 生成产品ID列表
        print("📦 生成产品ID列表...")
        product_ids = []
        
        # 使用一些已知的产品ID
        base_ids = ["5083982424", "1363194395", "1649269851", "2834567890", "1234567890"]
        
        # 生成随机产品ID
        for _ in range(max_products):
            if len(product_ids) < len(base_ids):
                product_ids.append(base_ids[len(product_ids)])
            else:
                # 生成10位随机数字ID
                random_id = str(random.randint(1000000000, 9999999999))
                product_ids.append(random_id)
        
        print(f"✅ 生成了 {len(product_ids)} 个产品ID")
        
        # 3. 批量收集令牌
        self.batch_collect_tokens(product_ids, seed_tokens, max_workers=8)
        
        # 4. 验证令牌
        if self.collected_tokens:
            verification_rate = self.verify_collected_tokens(sample_size=30)
        else:
            print("❌ 没有收集到令牌")
            return
        
        # 5. 保存令牌
        filename = self.save_tokens_to_excel()
        
        # 6. 最终统计
        print(f"\n🎉 收集周期完成!")
        print(f"📊 最终统计:")
        print(f"   - 请求总数: {self.stats['requests_made']}")
        print(f"   - 成功请求: {self.stats['successful_requests']}")
        print(f"   - 收集令牌: {self.stats['tokens_collected']}")
        print(f"   - 验证令牌: {self.stats['tokens_verified']}")
        print(f"   - 验证成功率: {verification_rate:.1f}%")
        print(f"   - 保存文件: {filename}")
        
        return filename

def main():
    """主函数"""
    collector = VerifiedTokenCollector()
    
    print("🎯 验证令牌收集器")
    print("="*60)
    print("这个工具通过真实请求收集经过验证的令牌")
    print("收集的令牌来自成功的HTTP响应，具有验证历史")
    print("="*60)
    
    try:
        # 运行收集周期
        result_file = collector.run_collection_cycle(
            target_token_count=1000,
            max_products=100
        )
        
        if result_file:
            print(f"\n✅ 收集完成！令牌已保存到: {result_file}")
            print(f"💡 使用建议:")
            print(f"   1. 这些令牌来自真实请求，具有验证历史")
            print(f"   2. 建议与原始令牌混合使用")
            print(f"   3. 可以直接替换到simple_proxy_crawler.py中使用")
        else:
            print(f"\n❌ 收集失败，请检查网络连接和代理设置")
    
    except KeyboardInterrupt:
        print(f"\n\n👋 用户中断收集过程")
    except Exception as e:
        print(f"\n❌ 收集过程中发生错误: {str(e)}")

if __name__ == '__main__':
    main()

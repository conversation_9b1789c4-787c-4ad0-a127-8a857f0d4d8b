# coding: utf-8
# 简单令牌生成器 - 基于成功请求逻辑的命令行版本
# File: simple_token_generator.py

import pandas as pd
import requests
import hashlib
import time
import random
import re
import threading
import urllib3
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

# 禁用警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class SimpleProxyManager:
    """简单的代理池管理器"""
    
    def __init__(self):
        self.proxies = []
        self.failed_proxies = set()
        self.current_index = 0
        self.lock = threading.Lock()
        self.load_proxies()
    
    def load_proxies(self):
        """从proxies.txt加载代理"""
        try:
            with open('proxies.txt', 'r', encoding='utf-8') as f:
                content = f.read()
            
            proxy_blocks = content.strip().split('\n\n')
            
            for block in proxy_blocks:
                if not block.strip():
                    continue
                
                lines = block.strip().split('\n')
                proxy_info = {}
                
                for line in lines:
                    if ':' in line:
                        key, value = line.split(':', 1)
                        proxy_info[key.strip()] = value.strip()
                
                if all(k in proxy_info for k in ['协议', '地址', '端口', '用户名', '密码']):
                    proxy_url = f"http://{proxy_info['用户名']}:{proxy_info['密码']}@{proxy_info['地址']}:{proxy_info['端口']}"
                    
                    proxy_config = {
                        "http": proxy_url,
                        "https": proxy_url,
                        "info": f"{proxy_info['地址']}:{proxy_info['端口']}"
                    }
                    self.proxies.append(proxy_config)
            
            print(f"成功加载 {len(self.proxies)} 个代理")
            
        except Exception as e:
            print(f"加载代理失败: {str(e)}")
            self.proxies = [self.get_original_proxy()]
    
    def get_original_proxy(self):
        """获取原始备用代理"""
        proxyAddr = "overseas.tunnel.qg.net:15655"
        authKey = "0MQGW8CU"
        password = "99AF4C18800B"
        proxyUrl = "http://%(user)s:%(password)s@%(server)s" % {
            "user": authKey,
            "password": password,
            "server": proxyAddr,
        }
        return {
            "http": proxyUrl,
            "https": proxyUrl,
            "info": proxyAddr
        }
    
    def get_proxy(self):
        """获取下一个可用代理"""
        with self.lock:
            if not self.proxies:
                return self.get_original_proxy()
            
            attempts = 0
            while attempts < len(self.proxies):
                proxy = self.proxies[self.current_index]
                self.current_index = (self.current_index + 1) % len(self.proxies)
                
                if proxy['info'] not in self.failed_proxies:
                    return proxy
                
                attempts += 1
            
            self.failed_proxies.clear()
            return self.get_original_proxy()
    
    def mark_proxy_failed(self, proxy_info):
        """标记代理失效"""
        with self.lock:
            self.failed_proxies.add(proxy_info)

def md5_encrypt(string):
    """MD5加密"""
    md5 = hashlib.md5()
    md5.update(string.encode('utf-8'))
    return md5.hexdigest()

def Headers():
    """生成随机请求头"""
    v1 = random.randint(100, 135)
    v2 = random.randint(10, 25)
    v3 = random.randint(400, 600)

    headers = {
        'name': '',
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Ch-Ua-Platform": "\"Windows\"",
        "Sec-Fetch-User": "?1",
        "Priority": "u=0, i",
        'Sec-Ch-Ua': f'"Microsoft Edge";v="{v1}", "Not-A.Brand";v="{v2}", "Chromium";v="{v1}"',
        f"F{md5_encrypt(str(time.time()))[:5]}": f"{md5_encrypt(str(random.randint(1, 10000)))}",
        "User-Agent": f"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/{v3}.36 (KHTML, like Gecko) Chrome/{v1}.0.0.0 Safari/{v3}.36 Edg/{v1}.0.0.0",
    }
    return headers

class SimpleTokenGenerator:
    """简单令牌生成器"""
    
    def __init__(self):
        self.proxy_manager = SimpleProxyManager()
        self.collected_tokens = set()
        self.verified_tokens = set()
        self.stats = {
            'requests_made': 0,
            'successful_requests': 0,
            'tokens_collected': 0,
            'tokens_verified': 0
        }
    
    def load_seed_tokens(self, count=50):
        """加载种子令牌"""
        try:
            df = pd.read_excel('令牌.xlsx')
            pxvid_list = df['pxvid'].dropna().astype(str).tolist()
            random.shuffle(pxvid_list)
            seed_tokens = pxvid_list[:count]
            print(f"✅ 加载了 {len(seed_tokens)} 个种子令牌")
            return seed_tokens
        except Exception as e:
            print(f"❌ 加载种子令牌失败: {str(e)}")
            return []
    
    def extract_tokens_from_response(self, response):
        """从响应中提取令牌"""
        tokens = set()
        
        # 从Set-Cookie头中提取
        set_cookie_header = response.headers.get('Set-Cookie', '')
        if set_cookie_header:
            pxvid_match = re.search(r'_pxvid=([^;]+)', set_cookie_header)
            if pxvid_match:
                tokens.add(pxvid_match.group(1))
            
            pxhd_match = re.search(r'_pxhd=([^;]+)', set_cookie_header)
            if pxhd_match:
                pxhd_value = pxhd_match.group(1)
                if ':' in pxhd_value:
                    extracted_token = pxhd_value.split(':')[-1]
                    tokens.add(extracted_token)
        
        # 从响应cookies中提取
        for cookie_name in ['_pxvid', '_pxhd', '_px3']:
            if cookie_name in response.cookies:
                cookie_value = response.cookies[cookie_name]
                if cookie_name == '_pxhd' and ':' in cookie_value:
                    extracted_token = cookie_value.split(':')[-1]
                    tokens.add(extracted_token)
                else:
                    tokens.add(cookie_value)
        
        # 从响应体中寻找11f0格式的令牌
        html = response.text
        original_pattern = r'[0-9a-f]{8}-[0-9a-f]{4}-11f0-[0-9a-f]{4}-[0-9a-f]{12}'
        found_tokens = re.findall(original_pattern, html)
        tokens.update(found_tokens)
        
        # 过滤有效令牌
        valid_tokens = []
        for token in tokens:
            if token and len(token) >= 30 and re.match(r'^[0-9a-f-]{36}$', token):
                valid_tokens.append(token)
        
        return valid_tokens
    
    def test_token_and_collect(self, token, product_id):
        """测试令牌并收集新令牌"""
        proxy_config = self.proxy_manager.get_proxy()
        
        try:
            url = f"https://www.walmart.com/ip/{product_id}"
            cookie = {"_pxvid": token}
            
            time.sleep(random.randint(2, 5))
            
            response = requests.get(
                url,
                cookies=cookie,
                headers=Headers(),
                proxies=proxy_config,
                timeout=15,
                verify=False
            )
            
            self.stats['requests_made'] += 1
            
            # 使用成功代码的判断逻辑
            success = (response.status_code == 200 and 
                      response.headers.get("Accept-CH") == 'Downlink, DPR')
            
            if success:
                self.stats['successful_requests'] += 1
                self.verified_tokens.add(token)
                
                # 从成功响应中提取新令牌
                new_tokens = self.extract_tokens_from_response(response)
                
                for new_token in new_tokens:
                    if new_token not in self.collected_tokens:
                        self.collected_tokens.add(new_token)
                        self.stats['tokens_collected'] += 1
                
                print(f"✅ 令牌测试成功，产品 {product_id}，收集到 {len(new_tokens)} 个新令牌")
                return True, new_tokens
            else:
                print(f"❌ 令牌测试失败，产品 {product_id}，状态码 {response.status_code}")
                return False, []
                
        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")
            if "proxy" in str(e).lower():
                self.proxy_manager.mark_proxy_failed(proxy_config['info'])
            return False, []
    
    def generate_similar_tokens(self, base_tokens, count):
        """基于成功令牌生成相似令牌"""
        if not base_tokens:
            return []
        
        new_tokens = []
        
        for _ in range(count):
            base_token = random.choice(list(base_tokens))
            
            if '-' in base_token and len(base_token) == 36:
                parts = base_token.split('-')
                if len(parts) == 5:
                    new_parts = []
                    for i, part in enumerate(parts):
                        if i == 2 and part == '11f0':
                            # 保持第三段为11f0
                            new_parts.append(part)
                        elif i == 1:
                            # 第二段稍微修改
                            base_val = int(part, 16)
                            new_val = base_val + random.randint(-500, 500)
                            new_val = max(0, min(0xFFFF, new_val))
                            new_parts.append(f"{new_val:04x}")
                        else:
                            # 其他段随机生成
                            new_part = ''.join(random.choice('0123456789abcdef') for _ in range(len(part)))
                            new_parts.append(new_part)
                    
                    new_token = '-'.join(new_parts)
                    new_tokens.append(new_token)
        
        return new_tokens
    
    def run_generation(self, target_count=1000, seed_count=50, max_iterations=3):
        """运行令牌生成"""
        print("🧬 简单令牌生成器")
        print("="*60)
        print(f"🎯 目标生成数量: {target_count}")
        print(f"🔑 种子令牌数量: {seed_count}")
        print(f"🔄 最大迭代次数: {max_iterations}")
        print("="*60)
        
        # 加载种子令牌
        seed_tokens = self.load_seed_tokens(seed_count)
        if not seed_tokens:
            print("❌ 无法加载种子令牌，生成终止")
            return
        
        # 测试产品ID
        test_products = [
            "5083982424", "1363194395", "1649269851",
            "100000001", "200000001", "300000001"
        ]
        
        # 添加随机产品ID
        for _ in range(14):
            random_id = str(random.randint(1000000000, 9999999999))
            test_products.append(random_id)
        
        # 开始生成过程
        for iteration in range(max_iterations):
            print(f"\n🔄 第 {iteration + 1} 轮收集和生成")
            print("-" * 40)
            
            # 当前可用令牌
            current_tokens = list(seed_tokens) + list(self.verified_tokens)
            test_tokens = random.sample(current_tokens, min(20, len(current_tokens)))
            
            # 并发测试令牌
            with ThreadPoolExecutor(max_workers=5) as executor:
                futures = []
                for token in test_tokens:
                    product_id = random.choice(test_products)
                    future = executor.submit(self.test_token_and_collect, token, product_id)
                    futures.append(future)
                
                for future in futures:
                    try:
                        future.result(timeout=30)
                    except Exception as e:
                        print(f"❌ 任务执行异常: {str(e)}")
            
            # 基于成功令牌生成更多令牌
            if self.verified_tokens:
                generation_count = min(200, target_count - len(self.collected_tokens))
                new_tokens = self.generate_similar_tokens(self.verified_tokens, generation_count)
                
                for token in new_tokens:
                    self.collected_tokens.add(token)
                    self.stats['tokens_collected'] += 1
                
                print(f"🧬 基于成功令牌生成了 {len(new_tokens)} 个新令牌")
            
            # 显示当前统计
            print(f"📊 当前统计:")
            print(f"   - 请求总数: {self.stats['requests_made']}")
            print(f"   - 成功请求: {self.stats['successful_requests']}")
            print(f"   - 收集令牌: {self.stats['tokens_collected']}")
            print(f"   - 验证令牌: {len(self.verified_tokens)}")
            
            if len(self.collected_tokens) >= target_count:
                print(f"🎉 已达到目标数量，提前结束")
                break
            
            time.sleep(3)  # 轮次间隔
        
        # 保存结果
        self.save_results()
        
        # 最终统计
        print(f"\n🎉 令牌生成完成!")
        print(f"📊 最终统计:")
        print(f"   - 请求总数: {self.stats['requests_made']}")
        print(f"   - 成功请求: {self.stats['successful_requests']}")
        print(f"   - 成功率: {self.stats['successful_requests']/max(1,self.stats['requests_made'])*100:.1f}%")
        print(f"   - 收集令牌: {len(self.collected_tokens)}")
        print(f"   - 验证令牌: {len(self.verified_tokens)}")
    
    def save_results(self):
        """保存生成结果"""
        if not self.collected_tokens:
            print("❌ 没有可保存的令牌")
            return
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"simple_generated_tokens_{timestamp}.xlsx"
        
        # 优先保存验证过的令牌
        tokens_to_save = list(self.verified_tokens) if self.verified_tokens else list(self.collected_tokens)
        
        try:
            df = pd.DataFrame({'pxvid': tokens_to_save})
            df.to_excel(filename, index=False)
            print(f"💾 成功保存 {len(tokens_to_save)} 个令牌到: {filename}")
        except Exception as e:
            print(f"❌ 保存失败: {str(e)}")

def main():
    """主函数"""
    print("🚀 简单令牌生成器启动")
    print("💡 基于成功请求逻辑生成验证令牌")
    print("📋 确保 令牌.xlsx 和 proxies.txt 文件存在")
    
    try:
        # 获取用户输入
        target_count = input("请输入目标生成数量 (默认1000): ").strip()
        target_count = int(target_count) if target_count else 1000
        
        seed_count = input("请输入种子令牌数量 (默认50): ").strip()
        seed_count = int(seed_count) if seed_count else 50
        
        generator = SimpleTokenGenerator()
        generator.run_generation(target_count=target_count, seed_count=seed_count)
        
    except KeyboardInterrupt:
        print("\n👋 用户中断程序")
    except Exception as e:
        print(f"❌ 程序执行异常: {str(e)}")

if __name__ == '__main__':
    main()

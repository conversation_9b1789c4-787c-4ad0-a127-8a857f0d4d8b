# coding: utf-8
# 基于1.py的简单代理池爬虫
# File: simple_proxy_crawler.py

import concurrent
from datetime import datetime
import pandas as pd
import urllib3
import requests
import hashlib
import time
import random
import re
from concurrent.futures import ThreadPoolExecutor
import json
import queue
import threading

# 禁用警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class SimpleProxyManager:
    """简单的代理池管理器"""
    
    def __init__(self):
        self.proxies = []
        self.failed_proxies = set()
        self.current_index = 0
        self.lock = threading.Lock()
        self.load_proxies()
    
    def load_proxies(self):
        """从proxies.txt加载代理"""
        try:
            with open('proxies.txt', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析代理信息
            proxy_blocks = content.strip().split('\n\n')
            
            for block in proxy_blocks:
                if not block.strip():
                    continue
                
                lines = block.strip().split('\n')
                proxy_info = {}
                
                for line in lines:
                    if ':' in line:
                        key, value = line.split(':', 1)
                        proxy_info[key.strip()] = value.strip()
                
                if all(k in proxy_info for k in ['协议', '地址', '端口', '用户名', '密码']):
                    # 转换为HTTP代理格式（requests对SOCKS5支持有限）
                    proxy_url = f"http://{proxy_info['用户名']}:{proxy_info['密码']}@{proxy_info['地址']}:{proxy_info['端口']}"
                    
                    proxy_config = {
                        "http": proxy_url,
                        "https": proxy_url,
                        "info": f"{proxy_info['地址']}:{proxy_info['端口']}"
                    }
                    self.proxies.append(proxy_config)
            
            print(f"成功加载 {len(self.proxies)} 个代理")
            
        except Exception as e:
            print(f"加载代理失败: {str(e)}")
            # 如果加载失败，使用原始的固定代理作为备用
            self.proxies = [self.get_original_proxy()]
    
    def get_original_proxy(self):
        """获取原始1.py中的固定代理作为备用"""
        proxyAddr = "overseas.tunnel.qg.net:15655"
        authKey = "0MQGW8CU"
        password = "99AF4C18800B"
        proxyUrl = "http://%(user)s:%(password)s@%(server)s" % {
            "user": authKey,
            "password": password,
            "server": proxyAddr,
        }
        return {
            "http": proxyUrl,
            "https": proxyUrl,
            "info": proxyAddr
        }
    
    def get_proxy(self):
        """获取下一个可用代理"""
        with self.lock:
            if not self.proxies:
                return self.get_original_proxy()
            
            # 找到下一个未失效的代理
            attempts = 0
            while attempts < len(self.proxies):
                proxy = self.proxies[self.current_index]
                self.current_index = (self.current_index + 1) % len(self.proxies)
                
                if proxy['info'] not in self.failed_proxies:
                    return proxy
                
                attempts += 1
            
            # 如果所有代理都失效了，重置失效列表并使用原始代理
            print("所有代理都已失效，重置代理池")
            self.failed_proxies.clear()
            return self.get_original_proxy()
    
    def mark_proxy_failed(self, proxy_info):
        """标记代理失效"""
        with self.lock:
            self.failed_proxies.add(proxy_info)
            print(f"标记代理失效: {proxy_info}")

# 全局代理管理器
proxy_manager = SimpleProxyManager()

def get_id():
    """获取产品ID列表"""
    name = "能跑多少跑多少1.xlsx"
    df = pd.read_excel(name)
    ids = df['ID'].astype(str).tolist()
    print("待查询ID列表:", ids[:10])  # 只显示前10个
    return ids

def load_pxvid_queue():
    """从令牌.xlsx加载pxvid并生成队列，每个vid重复5次"""
    try:
        df = pd.read_excel('令牌.xlsx')
        pxvid_list = df['pxvid'].dropna().astype(str).tolist()
        # 每个vid重复5次
        extended_vids = []
        for vid in pxvid_list:
            extended_vids.extend([vid] * 5)
        random.shuffle(extended_vids)  # 打乱顺序
        q = queue.Queue()
        for vid in extended_vids:
            q.put(vid)
        print(f"成功加载{len(extended_vids)}个pxvid到队列")
        return q
    except Exception as e:
        print(f"加载pxvid队列失败: {str(e)}")
        raise

def md5_encrypt(string):
    """MD5加密"""
    md5 = hashlib.md5()
    md5.update(string.encode('utf-8'))
    return md5.hexdigest()

def Headers():
    """生成随机请求头（保持1.py的逻辑）"""
    # 版本号
    v1 = random.randint(100, 135)
    v2 = random.randint(10, 25)
    v3 = random.randint(400, 600)

    headers = {
        'name': '',
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Ch-Ua-Platform": "\"Windows\"",
        "Sec-Fetch-User": "?1",
        "Priority": "u=0, i",
        'Sec-Ch-Ua': f'"Microsoft Edge";v="{v1}", "Not-A.Brand";v="{v2}", "Chromium";v="{v1}"',
        f"F{md5_encrypt(str(time.time()))[:5]}": f"{md5_encrypt(str(random.randint(1, 10000)))}",
        "User-Agent": f"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/{v3}.36 (KHTML, like Gecko) Chrome/{v1}.0.0.0 Safari/{v3}.36 Edg/{v1}.0.0.0",
    }
    return headers

def process_id(id, vid_queue):
    """处理单个产品ID（保持1.py的核心逻辑）"""
    row_data = {'ID': id}
    max_retries = 3

    # 从队列获取pxvid
    try:
        pxvid = vid_queue.get_nowait()
    except queue.Empty:
        row_data['Brand'] = 'No pxvid available'
        print(f"ID {id}: 无可用pxvid")
        return row_data

    for attempt in range(max_retries):
        # 获取代理
        proxy_config = proxy_manager.get_proxy()
        
        try:
            url = f"https://www.walmart.com/ip/{id}"
            cookie = {"_pxvid": pxvid}
            time.sleep(random.randint(5, 10))  # 保持1.py的延时

            response = requests.get(
                url,
                cookies=cookie,
                headers=Headers(),
                proxies=proxy_config,
                timeout=15,
                verify=False
            )

            # 保持1.py的成功判断逻辑
            if response.status_code == 200 and response.headers.get("Accept-CH") == 'Downlink, DPR':
                html = response.text.replace('\f', '').replace('\n', '').replace('\t', '')
                
                # 提取产品信息（保持1.py的提取逻辑）
                row_data['Brand'] = re.findall('"Brand","name":"(.*?)"', html)[0] if re.findall('"Brand","name":"(.*?)"', html) else ''
                row_data['Title'] = re.findall('"productName":"(.*?)"', html)[0] if re.findall('"productName":"(.*?)"',html) else ''
                row_data['LongDescription'] = re.findall('"longDescription":"(.*?)","shortDescription', html)[0] if re.findall('"longDescription":"(.*?)","shortDescription', html) else ''
                row_data['ProductDetails'] = re.findall('"shortDescription":"(.*?)","fulfillmentType":', html)[0] if re.findall('"shortDescription":"(.*?)","fulfillmentType":', html) else ''
                row_data['Link'] = re.findall('\{"@type":"Offer","url":"(.*?)",', html)[0] if re.findall( '\{"@type":"Offer","url":"(.*?)",', html) else ''
                row_data['zy'] = re.findall(',"sellerDisplayName":"(.*?)","', html)[0] if re.findall(',"sellerDisplayName":"(.*?)","', html) else ''
                row_data['price'] = re.findall('"priceCurrency":"USD","price":(.*?),"', html)[0] if re.findall('"priceCurrency":"USD","price":(.*?),"', html) else ''
                row_data['left'] = re.findall('"usecase":"SHIPPING","value":"(.*?)",', html)[0] if re.findall('"usecase":"SHIPPING","value":"(.*?)",', html) else ''
                row_data['wfs'] = re.findall('"wfsEnabled":(.*?),', html)[0] if re.findall('"wfsEnabled":(.*?),',html) else ''
                row_data['yunfei'] = re.findall('"shipPrice":{"price":(.*?),"', html)[0] if re.findall('"shipPrice":{"price":(.*?),"', html) else ''
                row_data['pingfen'] = re.findall('@type":"AggregateRating","ratingValue":(.*?),"', html)[0] if re.findall('@type":"AggregateRating","ratingValue":(.*?),"', html) else ''
                row_data['pinglun'] = re.findall('totalReviewsCountAsString":"(.*?)","', html)[0] if re.findall('totalReviewsCountAsString":"(.*?)","', html) else ''
                
                if row_data['zy'] == '':
                    row_data['Brand'] = 'ID失效或缺货'

                # 提取图片链接
                image_result = {'ID': id}
                all_images_match = re.search(r'"allImages":\[(.*?)\],', html, re.DOTALL)
                if all_images_match:
                    try:
                        images_data = json.loads(f'[{all_images_match.group(1)}]')
                        for idx, img in enumerate(images_data[:20], 1):
                            if 'url' in img:
                                image_result[f'Image_{idx}'] = img['url']
                    except Exception as e:
                        print(f"图片解析失败 ID: {id} - {str(e)}")
                row_data.update(image_result)
                
                print(f"✅ ID {id} 成功: {row_data.get('Brand', 'N/A')} (代理: {proxy_config['info']})")
                break  # 请求成功，退出重试循环
                
            else:
                print(f"❌ ID {id} 状态码 {response.status_code}, 第{attempt + 1}次重试 (代理: {proxy_config['info']})")
                if response.status_code == 404 and attempt == 2:
                    row_data['Brand'] = 'ID失效或缺货'
                elif response.status_code in [403, 429]:
                    # 可能是代理被封，标记失效
                    proxy_manager.mark_proxy_failed(proxy_config['info'])
                    
        except Exception as e:
            print(f"❌ ID {id} 请求异常: {str(e)}，第 {attempt + 1} 次重试 (代理: {proxy_config['info']})")
            if "proxy" in str(e).lower() or "connection" in str(e).lower():
                proxy_manager.mark_proxy_failed(proxy_config['info'])
            if attempt == 2:
                row_data['Brand'] = '请求失败'
    
    return row_data

def request_data():
    """主数据采集函数（保持1.py的逻辑）"""
    ids = get_id()
    all_results = []

    # 加载pxvid队列
    vid_queue = load_pxvid_queue()

    # 降低并发数，避免过于激进
    max_workers = 50  # 比1.py的100要保守一些

    print(f"🚀 开始简单代理池爬取，并发数: {max_workers}")
    print(f"📊 待处理产品数: {len(ids)}")

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 传递vid_queue到每个线程
        futures = [executor.submit(process_id, id, vid_queue) for id in ids]

        completed = 0
        for future in concurrent.futures.as_completed(futures):
            result = future.result()
            all_results.append(result)
            completed += 1

            # 显示进度
            success = "✅" if result.get('Brand') and result['Brand'] not in ['No pxvid available', '请求失败', 'ID失效或缺货'] else "❌"
            print(f"{success} 已完成ID {result['ID']} 的数据采集 ({completed}/{len(ids)})")

    # 生成结果文件（保持1.py的格式）
    base_columns = ['ID', 'Brand', 'Title', 'LongDescription', 'ProductDetails', 'Link', 'zy', 'price', 'left', 'wfs','yunfei', 'pingfen', 'pinglun']
    image_columns = sorted(set(key for result in all_results for key in result if key.startswith('Image_')))
    columns = base_columns + image_columns

    df = pd.DataFrame(all_results).reindex(columns=columns)
    filename = f'simple_proxy_result-{datetime.today().month}-{datetime.today().day}.xlsx'
    df.to_excel(filename, index=False)

    # 统计结果
    success_count = len([r for r in all_results if r.get('Brand') and r['Brand'] not in ['No pxvid available', '请求失败', 'ID失效或缺货']])

    print(f"\n🎉 简单代理池爬取完成！")
    print(f"📊 统计信息: 总数 {len(all_results)}, 成功 {success_count}, 成功率 {success_count/len(all_results)*100:.1f}%")
    print(f"📁 结果已保存到: {filename}")

    # 显示代理使用情况
    print(f"🔧 代理池状态: 总数 {len(proxy_manager.proxies)}, 失效 {len(proxy_manager.failed_proxies)}")

if __name__ == '__main__':
    start_time = time.time()
    try:
        request_data()
    except KeyboardInterrupt:
        print("\n用户中断程序")
    except Exception as e:
        print(f"程序执行出错: {str(e)}")
    finally:
        end_time = time.time()
        print(f"程序执行完成，总用时: {end_time - start_time:.2f} 秒")

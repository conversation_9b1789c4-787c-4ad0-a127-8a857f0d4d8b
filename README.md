# 增强版Walmart爬虫

这是一个基于原始1.py的增强版Walmart产品爬虫，主要改进了代理管理和反爬虫检测功能。

## 🚀 主要改进

### 1. 代理池管理
- ✅ 支持从`proxies.txt`加载多个SOCKS5代理
- ✅ 自动代理轮换，避免单点故障
- ✅ 代理健康检查和失效标记
- ✅ 线程安全的代理分配

### 2. 反爬虫检测与破解
- ✅ 检测PerimeterX、Cloudflare等反爬虫系统
- ✅ 智能识别403、429、503等封禁状态
- ✅ 动态User-Agent和请求头生成
- ✅ 请求指纹随机化
- ✅ 自适应重试策略

### 3. 性能优化
- ✅ 使用Session和连接池复用
- ✅ 智能延时机制
- ✅ 并发控制优化
- ✅ 详细的统计信息

### 4. 错误处理
- ✅ 完善的异常处理机制
- ✅ 详细的日志记录
- ✅ 自动错误恢复
- ✅ 失败原因分析

## 📁 文件结构

```
├── enhanced_crawler.py     # 主要的增强爬虫代码
├── test_proxy_pool.py     # 代理池测试脚本
├── usage_example.py       # 使用示例和演示
├── README.md             # 说明文档
├── proxies.txt           # 代理配置文件
├── 令牌.xlsx             # pxvid令牌文件
└── 能跑多少跑多少1.xlsx    # 待爬取的产品ID列表
```

## 🔧 环境要求

### Python依赖
```bash
pip install pandas requests urllib3 openpyxl
pip install requests[socks]  # SOCKS5代理支持
```

### 配置文件

#### 1. proxies.txt格式
```
协议: socks5
地址: **************
端口: 48361
用户名: 1BHVeuZ1WM
密码: 65tJbpVstb

协议: socks5
地址: **************
端口: 43562
用户名: Bix5uohS6v
密码: uLNPaIK5ov
```

#### 2. 令牌.xlsx
需要包含`pxvid`列，存储有效的PerimeterX令牌

#### 3. 能跑多少跑多少1.xlsx
需要包含`ID`列，存储待爬取的Walmart产品ID

## 🚀 快速开始

### 1. 测试代理池
```bash
python test_proxy_pool.py
```

### 2. 查看使用示例
```bash
python usage_example.py
```

### 3. 运行完整爬虫
```bash
python enhanced_crawler.py
```

## 📊 使用示例

### 基本使用
```python
from enhanced_crawler import request_data_enhanced

# 运行完整爬取流程
request_data_enhanced()
```

### 自定义使用
```python
from enhanced_crawler import ProxyManager, EnhancedRequester, process_id_enhanced
import queue

# 初始化组件
proxy_manager = ProxyManager()
requester = EnhancedRequester(proxy_manager)

# 创建pxvid队列
vid_queue = queue.Queue()
vid_queue.put("your_pxvid_here")

# 处理单个产品
result = process_id_enhanced("product_id", vid_queue, requester)
print(result)
```

## 🛡️ 反爬虫策略

### 检测机制
- **状态码检测**: 403, 429, 503等
- **内容检测**: PerimeterX, Cloudflare关键词
- **响应验证**: 检查预期内容是否存在

### 破解策略
- **代理轮换**: 自动切换IP避免封禁
- **请求头随机化**: 模拟真实浏览器
- **智能延时**: 避免请求频率过高
- **指纹随机化**: 防止浏览器指纹识别

## 📈 性能监控

### 日志信息
程序会输出详细的日志信息，包括：
- 代理使用情况
- 请求成功/失败状态
- 反爬虫检测结果
- 数据提取情况

### 统计信息
程序结束时会显示：
- 总处理数量
- 成功数量和成功率
- 执行时间

## ⚙️ 配置建议

### 线程数调整
```python
# 在enhanced_crawler.py中调整
with ThreadPoolExecutor(max_workers=50) as executor:  # 根据代理质量调整
```

### 延时设置
```python
# 在EnhancedRequester.make_request中调整
delay = random.uniform(3, 8)  # 根据需要调整延时范围
```

### 重试次数
```python
# 在make_request方法中调整
def make_request(self, url: str, cookies: Dict = None, max_retries: int = 5):
```

## 🔍 故障排除

### 常见问题

1. **代理连接失败**
   - 检查proxies.txt格式是否正确
   - 验证代理服务器是否可用
   - 确认网络连接正常

2. **pxvid无效**
   - 更新令牌.xlsx中的pxvid
   - 检查pxvid格式是否正确

3. **成功率低**
   - 检查代理质量
   - 调整请求延时
   - 更新反爬虫策略

4. **程序崩溃**
   - 查看详细错误日志
   - 检查依赖库是否完整安装
   - 验证配置文件格式

### 调试模式
```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📝 更新日志

### v2.0 (当前版本)
- 新增代理池管理功能
- 增强反爬虫检测和破解
- 优化性能和错误处理
- 添加详细日志和统计

### v1.0 (原始版本)
- 基础Walmart产品爬取功能
- 固定代理配置
- 基本错误处理

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## ⚠️ 免责声明

本工具仅供学习和研究使用，请遵守相关网站的robots.txt和服务条款。使用者需要自行承担使用风险。

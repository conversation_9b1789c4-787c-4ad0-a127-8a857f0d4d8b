# coding: utf-8
# Playwright安装脚本
# File: install_playwright.py

import subprocess
import sys
import logging
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def install_playwright():
    """安装Playwright及其依赖"""
    logger.info("🚀 开始安装Playwright...")
    
    try:
        # 安装playwright包
        logger.info("安装playwright包...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "playwright"])
        logger.info("✅ playwright包安装成功")
        
        # 安装浏览器
        logger.info("安装Chromium浏览器...")
        subprocess.check_call([sys.executable, "-m", "playwright", "install", "chromium"])
        logger.info("✅ Chromium浏览器安装成功")
        
        # 安装系统依赖（Linux/Mac）
        if os.name != 'nt':  # 非Windows系统
            logger.info("安装系统依赖...")
            subprocess.check_call([sys.executable, "-m", "playwright", "install-deps"])
            logger.info("✅ 系统依赖安装成功")
        
        logger.info("🎉 Playwright安装完成！")
        return True
        
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ 安装失败: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"❌ 安装过程中出现错误: {str(e)}")
        return False

def test_playwright():
    """测试Playwright是否正常工作"""
    logger.info("🧪 测试Playwright功能...")
    
    try:
        from playwright.sync_api import sync_playwright
        
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            page = browser.new_page()
            page.goto("https://www.google.com")
            title = page.title()
            browser.close()
            
            if "Google" in title:
                logger.info("✅ Playwright测试成功")
                return True
            else:
                logger.error("❌ Playwright测试失败：页面标题不正确")
                return False
                
    except ImportError:
        logger.error("❌ Playwright导入失败，请检查安装")
        return False
    except Exception as e:
        logger.error(f"❌ Playwright测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("🔧 Playwright安装和配置工具")
    
    # 安装Playwright
    if not install_playwright():
        logger.error("安装失败，请检查网络连接和权限")
        return False
    
    # 测试功能
    if not test_playwright():
        logger.error("测试失败，请检查安装")
        return False
    
    logger.info("🎉 Playwright安装和配置完成！")
    logger.info("现在可以运行高级反爬虫系统:")
    logger.info("  python advanced_anti_detection.py")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        logger.info("用户中断安装")
        sys.exit(1)
    except Exception as e:
        logger.error(f"安装过程中出现未预期的错误: {str(e)}")
        sys.exit(1)

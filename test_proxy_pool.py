# coding: utf-8
# 测试代理池功能
# File: test_proxy_pool.py

import requests
import time
from enhanced_crawler import ProxyManager, AntiCrawlerHandler
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_single_proxy(proxy_dict):
    """测试单个代理的连通性"""
    test_urls = [
        "http://httpbin.org/ip",
        "https://www.walmart.com",
        "https://httpbin.org/user-agent"
    ]
    
    for url in test_urls:
        try:
            headers = AntiCrawlerHandler.get_enhanced_headers()
            response = requests.get(
                url, 
                proxies=proxy_dict, 
                headers=headers,
                timeout=10,
                verify=False
            )
            
            if response.status_code == 200:
                logger.info(f"代理测试成功: {url} - 状态码: {response.status_code}")
                if "httpbin.org/ip" in url:
                    logger.info(f"代理IP: {response.json().get('origin', 'Unknown')}")
                return True
            else:
                logger.warning(f"代理测试失败: {url} - 状态码: {response.status_code}")
                
        except Exception as e:
            logger.error(f"代理测试异常: {url} - {str(e)}")
    
    return False

def test_proxy_pool():
    """测试代理池功能"""
    logger.info("开始测试代理池...")
    
    try:
        # 初始化代理管理器
        proxy_manager = ProxyManager()
        
        if not proxy_manager.proxies:
            logger.error("没有加载到任何代理")
            return
        
        logger.info(f"已加载 {len(proxy_manager.proxies)} 个代理")
        
        # 测试前5个代理
        working_proxies = 0
        test_count = min(5, len(proxy_manager.proxies))
        
        for i in range(test_count):
            logger.info(f"\n--- 测试代理 {i+1}/{test_count} ---")
            proxy = proxy_manager.get_proxy()
            
            if proxy:
                logger.info(f"获取到代理: {proxy}")
                if test_single_proxy(proxy):
                    working_proxies += 1
                    logger.info("✓ 代理工作正常")
                else:
                    logger.warning("✗ 代理无法正常工作")
                    proxy_manager.mark_proxy_failed(proxy)
            else:
                logger.error("无法获取代理")
            
            time.sleep(2)  # 避免请求过快
        
        logger.info(f"\n代理测试完成: {working_proxies}/{test_count} 个代理可用")
        
    except Exception as e:
        logger.error(f"代理池测试失败: {str(e)}")

def test_anti_crawler_detection():
    """测试反爬虫检测功能"""
    logger.info("\n开始测试反爬虫检测...")
    
    # 模拟不同类型的响应
    class MockResponse:
        def __init__(self, status_code, text="", headers=None):
            self.status_code = status_code
            self.text = text
            self.headers = headers or {}
    
    test_cases = [
        (MockResponse(200, "normal walmart content productName"), "正常响应"),
        (MockResponse(403, "Access Denied"), "403禁止访问"),
        (MockResponse(429, "Too Many Requests"), "429请求过多"),
        (MockResponse(200, "perimeterx detected"), "PerimeterX检测"),
        (MockResponse(200, "cloudflare protection", {"cf-ray": "123"}), "Cloudflare检测"),
        (MockResponse(200, "robot detected suspicious activity"), "机器人检测"),
        (MockResponse(200, "empty response"), "空响应"),
    ]
    
    for response, description in test_cases:
        is_blocked, reason = AntiCrawlerHandler.detect_anti_crawler(response)
        status = "🚫 被阻止" if is_blocked else "✅ 正常"
        logger.info(f"{description}: {status} - {reason}")

def test_enhanced_headers():
    """测试增强请求头生成"""
    logger.info("\n开始测试请求头生成...")
    
    for i in range(3):
        headers = AntiCrawlerHandler.get_enhanced_headers()
        logger.info(f"请求头 {i+1}:")
        logger.info(f"  User-Agent: {headers.get('User-Agent', 'N/A')}")
        logger.info(f"  Accept-Language: {headers.get('Accept-Language', 'N/A')}")
        logger.info(f"  Sec-Ch-Ua-Platform: {headers.get('Sec-Ch-Ua-Platform', 'N/A')}")
        
        # 检查随机指纹
        fingerprint_headers = [k for k in headers.keys() if k.startswith('X-')]
        if fingerprint_headers:
            logger.info(f"  随机指纹: {fingerprint_headers[0]}")
        
        time.sleep(1)

if __name__ == "__main__":
    logger.info("=== 代理池和反爬虫系统测试 ===")
    
    # 测试代理池
    test_proxy_pool()
    
    # 测试反爬虫检测
    test_anti_crawler_detection()
    
    # 测试请求头生成
    test_enhanced_headers()
    
    logger.info("\n=== 测试完成 ===")

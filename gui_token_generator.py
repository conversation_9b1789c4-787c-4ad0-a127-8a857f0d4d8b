# coding: utf-8
# GUI令牌生成器 - 基于成功请求逻辑生成验证令牌
# File: gui_token_generator.py

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import threading
import time
import random
import re
import queue
import pandas as pd
import requests
import hashlib
import urllib3
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
import json

# 禁用警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class SimpleProxyManager:
    """简单的代理池管理器（复用成功代码的逻辑）"""
    
    def __init__(self):
        self.proxies = []
        self.failed_proxies = set()
        self.current_index = 0
        self.lock = threading.Lock()
        self.load_proxies()
    
    def load_proxies(self):
        """从proxies.txt加载代理"""
        try:
            with open('proxies.txt', 'r', encoding='utf-8') as f:
                content = f.read()
            
            proxy_blocks = content.strip().split('\n\n')
            
            for block in proxy_blocks:
                if not block.strip():
                    continue
                
                lines = block.strip().split('\n')
                proxy_info = {}
                
                for line in lines:
                    if ':' in line:
                        key, value = line.split(':', 1)
                        proxy_info[key.strip()] = value.strip()
                
                if all(k in proxy_info for k in ['协议', '地址', '端口', '用户名', '密码']):
                    proxy_url = f"http://{proxy_info['用户名']}:{proxy_info['密码']}@{proxy_info['地址']}:{proxy_info['端口']}"
                    
                    proxy_config = {
                        "http": proxy_url,
                        "https": proxy_url,
                        "info": f"{proxy_info['地址']}:{proxy_info['端口']}"
                    }
                    self.proxies.append(proxy_config)
            
            print(f"成功加载 {len(self.proxies)} 个代理")
            
        except Exception as e:
            print(f"加载代理失败: {str(e)}")
            self.proxies = [self.get_original_proxy()]
    
    def get_original_proxy(self):
        """获取原始备用代理"""
        proxyAddr = "overseas.tunnel.qg.net:15655"
        authKey = "0MQGW8CU"
        password = "99AF4C18800B"
        proxyUrl = "http://%(user)s:%(password)s@%(server)s" % {
            "user": authKey,
            "password": password,
            "server": proxyAddr,
        }
        return {
            "http": proxyUrl,
            "https": proxyUrl,
            "info": proxyAddr
        }
    
    def get_proxy(self):
        """获取下一个可用代理"""
        with self.lock:
            if not self.proxies:
                return self.get_original_proxy()
            
            attempts = 0
            while attempts < len(self.proxies):
                proxy = self.proxies[self.current_index]
                self.current_index = (self.current_index + 1) % len(self.proxies)
                
                if proxy['info'] not in self.failed_proxies:
                    return proxy
                
                attempts += 1
            
            self.failed_proxies.clear()
            return self.get_original_proxy()
    
    def mark_proxy_failed(self, proxy_info):
        """标记代理失效"""
        with self.lock:
            self.failed_proxies.add(proxy_info)

def md5_encrypt(string):
    """MD5加密"""
    md5 = hashlib.md5()
    md5.update(string.encode('utf-8'))
    return md5.hexdigest()

def Headers():
    """生成随机请求头（复用成功代码的逻辑）"""
    v1 = random.randint(100, 135)
    v2 = random.randint(10, 25)
    v3 = random.randint(400, 600)

    headers = {
        'name': '',
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Ch-Ua-Platform": "\"Windows\"",
        "Sec-Fetch-User": "?1",
        "Priority": "u=0, i",
        'Sec-Ch-Ua': f'"Microsoft Edge";v="{v1}", "Not-A.Brand";v="{v2}", "Chromium";v="{v1}"',
        f"F{md5_encrypt(str(time.time()))[:5]}": f"{md5_encrypt(str(random.randint(1, 10000)))}",
        "User-Agent": f"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/{v3}.36 (KHTML, like Gecko) Chrome/{v1}.0.0.0 Safari/{v3}.36 Edg/{v1}.0.0.0",
    }
    return headers

class TokenCollector:
    """令牌收集器 - 基于成功请求逻辑"""
    
    def __init__(self, gui_callback=None):
        self.proxy_manager = SimpleProxyManager()
        self.gui_callback = gui_callback
        self.collected_tokens = set()
        self.verified_tokens = set()
        self.stats = {
            'requests_made': 0,
            'successful_requests': 0,
            'tokens_collected': 0,
            'tokens_verified': 0
        }
        self.stop_flag = False
    
    def log(self, message):
        """日志输出"""
        if self.gui_callback:
            self.gui_callback(message)
        else:
            print(message)
    
    def load_seed_tokens(self, count=50):
        """加载种子令牌"""
        try:
            df = pd.read_excel('令牌.xlsx')
            pxvid_list = df['pxvid'].dropna().astype(str).tolist()
            random.shuffle(pxvid_list)
            seed_tokens = pxvid_list[:count]
            self.log(f"✅ 加载了 {len(seed_tokens)} 个种子令牌")
            return seed_tokens
        except Exception as e:
            self.log(f"❌ 加载种子令牌失败: {str(e)}")
            return []
    
    def extract_tokens_from_response(self, response):
        """从响应中提取令牌"""
        tokens = set()
        
        # 1. 从Set-Cookie头中提取
        set_cookie_header = response.headers.get('Set-Cookie', '')
        if set_cookie_header:
            pxvid_match = re.search(r'_pxvid=([^;]+)', set_cookie_header)
            if pxvid_match:
                tokens.add(pxvid_match.group(1))
            
            pxhd_match = re.search(r'_pxhd=([^;]+)', set_cookie_header)
            if pxhd_match:
                pxhd_value = pxhd_match.group(1)
                if ':' in pxhd_value:
                    extracted_token = pxhd_value.split(':')[-1]
                    tokens.add(extracted_token)
        
        # 2. 从响应cookies中提取
        for cookie_name in ['_pxvid', '_pxhd', '_px3']:
            if cookie_name in response.cookies:
                cookie_value = response.cookies[cookie_name]
                if cookie_name == '_pxhd' and ':' in cookie_value:
                    extracted_token = cookie_value.split(':')[-1]
                    tokens.add(extracted_token)
                else:
                    tokens.add(cookie_value)
        
        # 3. 从响应体中寻找11f0格式的令牌
        html = response.text
        original_pattern = r'[0-9a-f]{8}-[0-9a-f]{4}-11f0-[0-9a-f]{4}-[0-9a-f]{12}'
        found_tokens = re.findall(original_pattern, html)
        tokens.update(found_tokens)
        
        # 过滤有效令牌
        valid_tokens = []
        for token in tokens:
            if token and len(token) >= 30 and re.match(r'^[0-9a-f-]{36}$', token):
                valid_tokens.append(token)
        
        return valid_tokens
    
    def test_token_and_collect(self, token, product_id):
        """测试令牌并收集新令牌（使用成功代码的逻辑）"""
        if self.stop_flag:
            return False, []
        
        proxy_config = self.proxy_manager.get_proxy()
        
        try:
            url = f"https://www.walmart.com/ip/{product_id}"
            cookie = {"_pxvid": token}
            
            # 使用成功代码的延时
            time.sleep(random.randint(2, 5))
            
            response = requests.get(
                url,
                cookies=cookie,
                headers=Headers(),
                proxies=proxy_config,
                timeout=15,
                verify=False
            )
            
            self.stats['requests_made'] += 1
            
            # 使用成功代码的判断逻辑
            success = (response.status_code == 200 and 
                      response.headers.get("Accept-CH") == 'Downlink, DPR')
            
            if success:
                self.stats['successful_requests'] += 1
                self.verified_tokens.add(token)
                
                # 从成功响应中提取新令牌
                new_tokens = self.extract_tokens_from_response(response)
                
                for new_token in new_tokens:
                    if new_token not in self.collected_tokens:
                        self.collected_tokens.add(new_token)
                        self.stats['tokens_collected'] += 1
                
                self.log(f"✅ 令牌测试成功，产品 {product_id}，收集到 {len(new_tokens)} 个新令牌")
                return True, new_tokens
            else:
                self.log(f"❌ 令牌测试失败，产品 {product_id}，状态码 {response.status_code}")
                return False, []
                
        except Exception as e:
            self.log(f"❌ 请求异常: {str(e)}")
            if "proxy" in str(e).lower():
                self.proxy_manager.mark_proxy_failed(proxy_config['info'])
            return False, []
    
    def generate_similar_tokens(self, base_tokens, count):
        """基于成功令牌生成相似令牌"""
        if not base_tokens:
            return []
        
        new_tokens = []
        
        for _ in range(count):
            if self.stop_flag:
                break
                
            base_token = random.choice(list(base_tokens))
            
            if '-' in base_token and len(base_token) == 36:
                parts = base_token.split('-')
                if len(parts) == 5:
                    # 生成新的各段
                    new_parts = []
                    for i, part in enumerate(parts):
                        if i == 2 and part == '11f0':
                            # 保持第三段为11f0
                            new_parts.append(part)
                        elif i == 1:
                            # 第二段稍微修改（可能包含时间信息）
                            base_val = int(part, 16)
                            new_val = base_val + random.randint(-500, 500)
                            new_val = max(0, min(0xFFFF, new_val))
                            new_parts.append(f"{new_val:04x}")
                        else:
                            # 其他段随机生成
                            new_part = ''.join(random.choice('0123456789abcdef') for _ in range(len(part)))
                            new_parts.append(new_part)
                    
                    new_token = '-'.join(new_parts)
                    new_tokens.append(new_token)
        
        return new_tokens

class TokenGeneratorGUI:
    """令牌生成器GUI界面"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("智能令牌生成器 - 基于成功请求逻辑")
        self.root.geometry("800x700")
        self.root.resizable(True, True)

        # 设置样式
        style = ttk.Style()
        style.theme_use('clam')

        self.collector = None
        self.generation_thread = None
        self.is_running = False

        self.setup_ui()

    def setup_ui(self):
        """设置UI界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # 标题
        title_label = ttk.Label(main_frame, text="🧬 智能令牌生成器", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 10))

        subtitle_label = ttk.Label(main_frame, text="基于成功请求逻辑生成验证令牌", font=("Arial", 10))
        subtitle_label.grid(row=1, column=0, columnspan=3, pady=(0, 20))

        # 参数设置区域
        params_frame = ttk.LabelFrame(main_frame, text="生成参数", padding="10")
        params_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        params_frame.columnconfigure(1, weight=1)

        # 生成数量
        ttk.Label(params_frame, text="目标生成数量:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.target_count = tk.IntVar(value=1000)
        target_scale = ttk.Scale(params_frame, from_=100, to=5000, variable=self.target_count, orient=tk.HORIZONTAL)
        target_scale.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        self.target_label = ttk.Label(params_frame, text="1000")
        self.target_label.grid(row=0, column=2, sticky=tk.W)
        target_scale.configure(command=self.update_target_label)

        # 种子令牌数量
        ttk.Label(params_frame, text="种子令牌数量:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        self.seed_count = tk.IntVar(value=50)
        seed_scale = ttk.Scale(params_frame, from_=10, to=200, variable=self.seed_count, orient=tk.HORIZONTAL)
        seed_scale.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 10), pady=(5, 0))
        self.seed_label = ttk.Label(params_frame, text="50")
        self.seed_label.grid(row=1, column=2, sticky=tk.W, pady=(5, 0))
        seed_scale.configure(command=self.update_seed_label)

        # 测试产品数量
        ttk.Label(params_frame, text="测试产品数量:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        self.product_count = tk.IntVar(value=20)
        product_scale = ttk.Scale(params_frame, from_=5, to=100, variable=self.product_count, orient=tk.HORIZONTAL)
        product_scale.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(0, 10), pady=(5, 0))
        self.product_label = ttk.Label(params_frame, text="20")
        self.product_label.grid(row=2, column=2, sticky=tk.W, pady=(5, 0))
        product_scale.configure(command=self.update_product_label)

        # 并发线程数
        ttk.Label(params_frame, text="并发线程数:").grid(row=3, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        self.thread_count = tk.IntVar(value=5)
        thread_scale = ttk.Scale(params_frame, from_=1, to=20, variable=self.thread_count, orient=tk.HORIZONTAL)
        thread_scale.grid(row=3, column=1, sticky=(tk.W, tk.E), padx=(0, 10), pady=(5, 0))
        self.thread_label = ttk.Label(params_frame, text="5")
        self.thread_label.grid(row=3, column=2, sticky=tk.W, pady=(5, 0))
        thread_scale.configure(command=self.update_thread_label)

        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=3, column=0, columnspan=3, pady=(0, 10))

        self.start_button = ttk.Button(control_frame, text="🚀 开始生成", command=self.start_generation)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))

        self.stop_button = ttk.Button(control_frame, text="⏹️ 停止", command=self.stop_generation, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))

        self.clear_button = ttk.Button(control_frame, text="🗑️ 清空日志", command=self.clear_log)
        self.clear_button.pack(side=tk.LEFT, padx=(0, 10))

        self.save_button = ttk.Button(control_frame, text="💾 保存结果", command=self.save_results, state=tk.DISABLED)
        self.save_button.pack(side=tk.LEFT)

        # 进度和统计区域
        progress_frame = ttk.LabelFrame(main_frame, text="进度和统计", padding="10")
        progress_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        progress_frame.columnconfigure(1, weight=1)

        # 进度条
        ttk.Label(progress_frame, text="生成进度:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        self.progress_label = ttk.Label(progress_frame, text="0%")
        self.progress_label.grid(row=0, column=2, sticky=tk.W)

        # 统计信息
        stats_frame = ttk.Frame(progress_frame)
        stats_frame.grid(row=1, column=0, columnspan=3, pady=(10, 0))

        self.stats_labels = {}
        stats_info = [
            ("请求总数", "requests_made"),
            ("成功请求", "successful_requests"),
            ("收集令牌", "tokens_collected"),
            ("验证令牌", "tokens_verified")
        ]

        for i, (label_text, key) in enumerate(stats_info):
            ttk.Label(stats_frame, text=f"{label_text}:").grid(row=i//2, column=(i%2)*2, sticky=tk.W, padx=(0, 5), pady=2)
            self.stats_labels[key] = ttk.Label(stats_frame, text="0", foreground="blue")
            self.stats_labels[key].grid(row=i//2, column=(i%2)*2+1, sticky=tk.W, padx=(0, 20), pady=2)

        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="实时日志", padding="10")
        log_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(5, weight=1)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))

    def update_target_label(self, value):
        """更新目标数量标签"""
        self.target_label.config(text=str(int(float(value))))

    def update_seed_label(self, value):
        """更新种子数量标签"""
        self.seed_label.config(text=str(int(float(value))))

    def update_product_label(self, value):
        """更新产品数量标签"""
        self.product_label.config(text=str(int(float(value))))

    def update_thread_label(self, value):
        """更新线程数量标签"""
        self.thread_label.config(text=str(int(float(value))))

    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, formatted_message)
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def update_stats(self, stats):
        """更新统计信息"""
        for key, label in self.stats_labels.items():
            label.config(text=str(stats.get(key, 0)))

        # 更新进度
        target = self.target_count.get()
        collected = stats.get('tokens_collected', 0)
        progress = min(100, (collected / target) * 100) if target > 0 else 0
        self.progress_var.set(progress)
        self.progress_label.config(text=f"{progress:.1f}%")

        # 更新状态
        if stats.get('requests_made', 0) > 0:
            success_rate = (stats.get('successful_requests', 0) / stats['requests_made']) * 100
            self.status_var.set(f"运行中 - 成功率: {success_rate:.1f}%")

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)

    def start_generation(self):
        """开始生成令牌"""
        if self.is_running:
            return

        self.is_running = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.save_button.config(state=tk.DISABLED)

        # 清空之前的统计
        for label in self.stats_labels.values():
            label.config(text="0")
        self.progress_var.set(0)
        self.progress_label.config(text="0%")

        # 启动生成线程
        self.generation_thread = threading.Thread(target=self.run_generation, daemon=True)
        self.generation_thread.start()

    def stop_generation(self):
        """停止生成"""
        if self.collector:
            self.collector.stop_flag = True

        self.is_running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.save_button.config(state=tk.NORMAL)
        self.status_var.set("已停止")
        self.log_message("🛑 用户停止了生成过程")

    def run_generation(self):
        """运行令牌生成过程"""
        try:
            self.collector = TokenCollector(gui_callback=self.log_message)

            # 获取参数
            target_count = self.target_count.get()
            seed_count = self.seed_count.get()
            product_count = self.product_count.get()
            thread_count = self.thread_count.get()

            self.log_message(f"🎯 开始令牌生成任务")
            self.log_message(f"📊 目标数量: {target_count}, 种子数量: {seed_count}")
            self.log_message(f"📦 测试产品数: {product_count}, 并发线程: {thread_count}")

            # 加载种子令牌
            seed_tokens = self.collector.load_seed_tokens(seed_count)
            if not seed_tokens:
                self.log_message("❌ 无法加载种子令牌，生成终止")
                self.stop_generation()
                return

            # 生成测试产品ID
            test_products = [
                "5083982424", "1363194395", "1649269851",
                "100000001", "200000001", "300000001"
            ]

            # 添加随机产品ID
            for _ in range(product_count - len(test_products)):
                random_id = str(random.randint(1000000000, 9999999999))
                test_products.append(random_id)

            test_products = test_products[:product_count]

            # 开始收集和生成过程
            iteration = 0
            max_iterations = 5

            while (len(self.collector.collected_tokens) < target_count and
                   iteration < max_iterations and
                   not self.collector.stop_flag):

                iteration += 1
                self.log_message(f"🔄 第 {iteration} 轮收集和生成")

                # 测试种子令牌并收集新令牌
                current_tokens = list(seed_tokens) + list(self.collector.verified_tokens)
                test_tokens = random.sample(current_tokens, min(20, len(current_tokens)))

                with ThreadPoolExecutor(max_workers=thread_count) as executor:
                    futures = []
                    for token in test_tokens:
                        if self.collector.stop_flag:
                            break
                        product_id = random.choice(test_products)
                        future = executor.submit(self.collector.test_token_and_collect, token, product_id)
                        futures.append(future)

                    for future in futures:
                        if self.collector.stop_flag:
                            break
                        try:
                            future.result(timeout=30)
                        except Exception as e:
                            self.log_message(f"❌ 任务执行异常: {str(e)}")

                        # 更新统计
                        self.root.after(0, self.update_stats, self.collector.stats)

                # 基于收集到的令牌生成更多令牌
                if self.collector.verified_tokens and not self.collector.stop_flag:
                    generation_count = min(100, target_count - len(self.collector.collected_tokens))
                    new_tokens = self.collector.generate_similar_tokens(
                        self.collector.verified_tokens,
                        generation_count
                    )

                    for token in new_tokens:
                        self.collector.collected_tokens.add(token)
                        self.collector.stats['tokens_collected'] += 1

                    self.log_message(f"🧬 基于成功令牌生成了 {len(new_tokens)} 个新令牌")
                    self.root.after(0, self.update_stats, self.collector.stats)

                time.sleep(2)  # 轮次间隔

            # 生成完成
            if not self.collector.stop_flag:
                self.log_message(f"🎉 令牌生成完成!")
                self.log_message(f"📊 最终统计: 收集 {len(self.collector.collected_tokens)} 个令牌")
                self.log_message(f"✅ 验证 {len(self.collector.verified_tokens)} 个令牌")

                self.root.after(0, self.stop_generation)

        except Exception as e:
            self.log_message(f"❌ 生成过程异常: {str(e)}")
            self.root.after(0, self.stop_generation)

    def save_results(self):
        """保存生成结果"""
        if not self.collector or not self.collector.collected_tokens:
            messagebox.showwarning("警告", "没有可保存的令牌数据")
            return

        # 选择保存路径
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        default_filename = f"generated_tokens_{timestamp}.xlsx"

        filename = filedialog.asksaveasfilename(
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
            initialvalue=default_filename
        )

        if filename:
            try:
                # 优先保存验证过的令牌
                tokens_to_save = list(self.collector.verified_tokens) if self.collector.verified_tokens else list(self.collector.collected_tokens)

                df = pd.DataFrame({'pxvid': tokens_to_save})
                df.to_excel(filename, index=False)

                self.log_message(f"💾 成功保存 {len(tokens_to_save)} 个令牌到: {filename}")
                messagebox.showinfo("成功", f"已保存 {len(tokens_to_save)} 个令牌到:\n{filename}")

            except Exception as e:
                error_msg = f"保存失败: {str(e)}"
                self.log_message(f"❌ {error_msg}")
                messagebox.showerror("错误", error_msg)

    def run(self):
        """运行GUI"""
        self.log_message("🚀 智能令牌生成器已启动")
        self.log_message("💡 请设置生成参数，然后点击'开始生成'")
        self.log_message("📋 确保 令牌.xlsx 和 proxies.txt 文件存在")
        self.root.mainloop()

def main():
    """主函数"""
    app = TokenGeneratorGUI()
    app.run()

if __name__ == '__main__':
    main()

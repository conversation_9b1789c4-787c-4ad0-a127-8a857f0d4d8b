# coding: utf-8
# Project: Enhanced Walmart Crawler with Proxy Pool and Anti-Crawler Detection
# File: enhanced_crawler.py
# Author: Enhanced by AI
# Date: 2025/7/28
# IDE: PyCharm

import concurrent
from datetime import datetime
import pandas as pd
import urllib3
import requests
import hashlib
import time
import random
import re
from concurrent.futures import ThreadPoolExecutor
import json
import queue
import threading
from typing import List, Dict, Optional, Tuple
import logging
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 禁用警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProxyManager:
    """代理池管理器"""
    
    def __init__(self, proxy_file: str = "proxies.txt"):
        self.proxy_file = proxy_file
        self.proxies = []
        self.current_index = 0
        self.failed_proxies = set()
        self.lock = threading.Lock()
        self.load_proxies()
    
    def load_proxies(self):
        """从文件加载代理列表"""
        try:
            with open(self.proxy_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析代理信息
            proxy_blocks = content.strip().split('\n\n')
            for block in proxy_blocks:
                if not block.strip():
                    continue
                
                lines = block.strip().split('\n')
                proxy_info = {}
                
                for line in lines:
                    if '协议:' in line:
                        proxy_info['protocol'] = line.split('协议:')[1].strip()
                    elif '地址:' in line:
                        proxy_info['host'] = line.split('地址:')[1].strip()
                    elif '端口:' in line:
                        proxy_info['port'] = line.split('端口:')[1].strip()
                    elif '用户名:' in line:
                        proxy_info['username'] = line.split('用户名:')[1].strip()
                    elif '密码:' in line:
                        proxy_info['password'] = line.split('密码:')[1].strip()
                
                if all(key in proxy_info for key in ['protocol', 'host', 'port', 'username', 'password']):
                    self.proxies.append(proxy_info)
            
            logger.info(f"成功加载 {len(self.proxies)} 个代理")
            
        except Exception as e:
            logger.error(f"加载代理文件失败: {str(e)}")
            raise
    
    def get_proxy(self) -> Optional[Dict]:
        """获取下一个可用代理"""
        with self.lock:
            if not self.proxies:
                return None
            
            # 尝试找到一个未失效的代理
            attempts = 0
            while attempts < len(self.proxies):
                proxy = self.proxies[self.current_index]
                proxy_key = f"{proxy['host']}:{proxy['port']}"
                
                if proxy_key not in self.failed_proxies:
                    # 构造代理URL
                    proxy_url = f"{proxy['protocol']}://{proxy['username']}:{proxy['password']}@{proxy['host']}:{proxy['port']}"
                    proxy_dict = {
                        "http": proxy_url,
                        "https": proxy_url
                    }
                    
                    self.current_index = (self.current_index + 1) % len(self.proxies)
                    return proxy_dict
                
                self.current_index = (self.current_index + 1) % len(self.proxies)
                attempts += 1
            
            # 如果所有代理都失效了，清空失效列表重新开始
            if len(self.failed_proxies) >= len(self.proxies):
                logger.warning("所有代理都已失效，重置失效列表")
                self.failed_proxies.clear()
                return self.get_proxy()
            
            return None
    
    def mark_proxy_failed(self, proxy_dict: Dict):
        """标记代理为失效"""
        if not proxy_dict:
            return
        
        try:
            # 从代理URL中提取host:port
            proxy_url = proxy_dict.get('http', '')
            if '@' in proxy_url:
                host_port = proxy_url.split('@')[1]
                with self.lock:
                    self.failed_proxies.add(host_port)
                    logger.warning(f"标记代理失效: {host_port}")
        except Exception as e:
            logger.error(f"标记代理失效时出错: {str(e)}")

class AntiCrawlerHandler:
    """反爬虫检测和处理器"""
    
    @staticmethod
    def detect_anti_crawler(response: requests.Response) -> Tuple[bool, str]:
        """检测反爬虫机制"""
        
        # 检查状态码
        if response.status_code == 403:
            return True, "403 Forbidden - 可能被IP封禁"
        elif response.status_code == 429:
            return True, "429 Too Many Requests - 请求频率过高"
        elif response.status_code == 503:
            return True, "503 Service Unavailable - 服务不可用"
        
        # 检查响应内容
        content = response.text.lower()
        
        # PerimeterX检测
        if 'perimeterx' in content or '_px' in content:
            return True, "PerimeterX反爬虫系统"
        
        # Cloudflare检测
        if 'cloudflare' in content or 'cf-ray' in response.headers:
            return True, "Cloudflare反爬虫系统"
        
        # 通用反爬虫检测
        anti_crawler_keywords = [
            'access denied', 'blocked', 'captcha', 'verification',
            'robot', 'bot detected', 'suspicious activity'
        ]
        
        for keyword in anti_crawler_keywords:
            if keyword in content:
                return True, f"检测到反爬虫关键词: {keyword}"
        
        # 检查是否缺少预期的内容
        if response.status_code == 200:
            # 检查是否有预期的Walmart内容
            if 'walmart' not in content and 'productName' not in content:
                return True, "响应内容异常 - 可能被重定向或阻止"
        
        return False, "未检测到反爬虫"
    
    @staticmethod
    def get_enhanced_headers() -> Dict:
        """生成增强的请求头"""
        # 版本号随机化
        chrome_version = random.randint(120, 130)
        webkit_version = random.randint(537, 540)
        edge_version = chrome_version
        
        # 随机User-Agent池
        user_agents = [
            f"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/{webkit_version}.36 (KHTML, like Gecko) Chrome/{chrome_version}.0.0.0 Safari/{webkit_version}.36",
            f"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/{webkit_version}.36 (KHTML, like Gecko) Chrome/{chrome_version}.0.0.0 Safari/{webkit_version}.36 Edg/{edge_version}.0.0.0",
            f"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/{webkit_version}.36 (KHTML, like Gecko) Chrome/{chrome_version}.0.0.0 Safari/{webkit_version}.36",
            f"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/{webkit_version}.36 (KHTML, like Gecko) Chrome/{chrome_version}.0.0.0 Safari/{webkit_version}.36"
        ]
        
        headers = {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": random.choice([
                "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7",
                "en-GB,en;q=0.9,en-US;q=0.8,zh-CN;q=0.7"
            ]),
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Cache-Control": random.choice(["no-cache", "max-age=0", ""]),
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Ch-Ua-Platform": f'"{random.choice(["Windows", "macOS", "Linux"])}"',
            "Sec-Ch-Ua-Mobile": "?0",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": random.choice(["none", "same-origin", "cross-site"]),
            "Sec-Fetch-User": "?1",
            "Priority": "u=0, i",
            "User-Agent": random.choice(user_agents),
            # 添加随机指纹
            f"X-{hashlib.md5(str(time.time()).encode()).hexdigest()[:8]}": hashlib.md5(str(random.randint(1, 10000)).encode()).hexdigest()
        }
        
        # 随机添加一些可选头部
        optional_headers = {
            "DNT": "1",
            "Sec-GPC": "1",
            "X-Requested-With": "XMLHttpRequest"
        }
        
        for key, value in optional_headers.items():
            if random.random() > 0.5:
                headers[key] = value
        
        return headers

class EnhancedRequester:
    """增强的请求处理器"""
    
    def __init__(self, proxy_manager: ProxyManager):
        self.proxy_manager = proxy_manager
        self.anti_crawler_handler = AntiCrawlerHandler()
        self.session = self._create_session()
    
    def _create_session(self) -> requests.Session:
        """创建配置好的session"""
        session = requests.Session()
        
        # 配置重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session
    
    def make_request(self, url: str, cookies: Dict = None, max_retries: int = 5) -> Tuple[Optional[requests.Response], str]:
        """发送请求，包含代理轮换和反爬虫处理"""
        
        for attempt in range(max_retries):
            proxy = self.proxy_manager.get_proxy()
            if not proxy:
                return None, "无可用代理"
            
            try:
                headers = self.anti_crawler_handler.get_enhanced_headers()
                
                # 添加随机延时
                delay = random.uniform(3, 8)
                time.sleep(delay)
                
                response = self.session.get(
                    url,
                    headers=headers,
                    cookies=cookies or {},
                    proxies=proxy,
                    timeout=15,
                    verify=False,
                    allow_redirects=True
                )
                
                # 检测反爬虫
                is_blocked, reason = self.anti_crawler_handler.detect_anti_crawler(response)
                
                if is_blocked:
                    logger.warning(f"检测到反爬虫: {reason}, 尝试切换代理")
                    self.proxy_manager.mark_proxy_failed(proxy)
                    continue
                
                # 检查响应是否符合预期
                if response.status_code == 200 and response.headers.get("Accept-CH") == 'Downlink, DPR':
                    return response, "成功"
                elif response.status_code == 404:
                    return response, "404 - 产品不存在"
                else:
                    logger.warning(f"响应异常: 状态码 {response.status_code}")
                    continue
                    
            except requests.exceptions.ProxyError:
                logger.warning(f"代理连接失败: {proxy}")
                self.proxy_manager.mark_proxy_failed(proxy)
                continue
            except requests.exceptions.Timeout:
                logger.warning(f"请求超时: {proxy}")
                continue
            except Exception as e:
                logger.error(f"请求异常: {str(e)}")
                continue
        
        return None, f"所有重试都失败了 ({max_retries} 次)"

# 保持原有的辅助函数
name = "能跑多少跑多少1.xlsx"

def get_id():
    df = pd.read_excel(name)
    ids = df['ID'].astype(str).tolist()
    print("待查询ID列表:", ids)
    return ids

def load_pxvid_queue():
    """从pxvid.xlsx加载pxvid并生成队列，每个vid重复5次"""
    try:
        df = pd.read_excel('令牌.xlsx')
        pxvid_list = df['pxvid'].dropna().astype(str).tolist()
        extended_vids = []
        for vid in pxvid_list:
            extended_vids.extend([vid] * 5)
        random.shuffle(extended_vids)
        q = queue.Queue()
        for vid in extended_vids:
            q.put(vid)
        print(f"成功加载{len(extended_vids)}个pxvid到队列")
        return q
    except Exception as e:
        print(f"加载pxvid队列失败: {str(e)}")
        raise

def md5_encrypt(string):
    md5 = hashlib.md5()
    md5.update(string.encode('utf-8'))
    return md5.hexdigest()

def process_id_enhanced(id_val, vid_queue, requester: EnhancedRequester):
    """增强版的ID处理函数"""
    row_data = {'ID': id_val}

    # 从队列获取pxvid
    try:
        pxvid = vid_queue.get_nowait()
    except queue.Empty:
        row_data['Brand'] = 'No pxvid available'
        logger.warning(f"ID {id_val}: 无可用pxvid")
        return row_data

    url = f"https://www.walmart.com/ip/{id_val}"
    cookie = {"_pxvid": pxvid}

    # 使用增强请求器发送请求
    response, status = requester.make_request(url, cookies=cookie)

    if response is None:
        row_data['Brand'] = f'请求失败: {status}'
        logger.error(f"ID {id_val}: {status}")
        return row_data

    if response.status_code == 404:
        row_data['Brand'] = 'ID失效或缺货'
        logger.info(f"ID {id_val}: 产品不存在")
        return row_data

    if response.status_code != 200:
        row_data['Brand'] = f'HTTP {response.status_code}'
        logger.warning(f"ID {id_val}: HTTP {response.status_code}")
        return row_data

    try:
        html = response.text.replace('\f', '').replace('\n', '').replace('\t', '')

        # 提取产品信息
        row_data['Brand'] = re.findall('"Brand","name":"(.*?)"', html)[0] if re.findall('"Brand","name":"(.*?)"', html) else ''
        row_data['Title'] = re.findall('"productName":"(.*?)"', html)[0] if re.findall('"productName":"(.*?)"', html) else ''
        row_data['LongDescription'] = re.findall('"longDescription":"(.*?)","shortDescription', html)[0] if re.findall('"longDescription":"(.*?)","shortDescription', html) else ''
        row_data['ProductDetails'] = re.findall('"shortDescription":"(.*?)","fulfillmentType":', html)[0] if re.findall('"shortDescription":"(.*?)","fulfillmentType":', html) else ''
        row_data['Link'] = re.findall('\{"@type":"Offer","url":"(.*?)",', html)[0] if re.findall('\{"@type":"Offer","url":"(.*?)",', html) else ''
        row_data['zy'] = re.findall(',"sellerDisplayName":"(.*?)","', html)[0] if re.findall(',"sellerDisplayName":"(.*?)","', html) else ''
        row_data['price'] = re.findall('"priceCurrency":"USD","price":(.*?),"', html)[0] if re.findall('"priceCurrency":"USD","price":(.*?),"', html) else ''
        row_data['left'] = re.findall('"usecase":"SHIPPING","value":"(.*?)",', html)[0] if re.findall('"usecase":"SHIPPING","value":"(.*?)",', html) else ''
        row_data['wfs'] = re.findall('"wfsEnabled":(.*?),', html)[0] if re.findall('"wfsEnabled":(.*?),', html) else ''
        row_data['yunfei'] = re.findall('"shipPrice":{"price":(.*?),"', html)[0] if re.findall('"shipPrice":{"price":(.*?),"', html) else ''
        row_data['pingfen'] = re.findall('@type":"AggregateRating","ratingValue":(.*?),"', html)[0] if re.findall('@type":"AggregateRating","ratingValue":(.*?),"', html) else ''
        row_data['pinglun'] = re.findall('totalReviewsCountAsString":"(.*?)","', html)[0] if re.findall('totalReviewsCountAsString":"(.*?)","', html) else ''

        if row_data['zy'] == '':
            row_data['Brand'] = 'ID失效或缺货'

        # 提取图片链接
        image_result = {'ID': id_val}
        all_images_match = re.search(r'"allImages":\[(.*?)\],', html, re.DOTALL)
        if all_images_match:
            try:
                images_data = json.loads(f'[{all_images_match.group(1)}]')
                for idx, img in enumerate(images_data[:20], 1):
                    if 'url' in img:
                        image_result[f'Image_{idx}'] = img['url']
            except Exception as e:
                logger.error(f"图片解析失败 ID: {id_val} - {str(e)}")

        row_data.update(image_result)
        logger.info(f"ID {id_val}: 数据提取成功")

    except Exception as e:
        row_data['Brand'] = f'数据解析失败: {str(e)}'
        logger.error(f"ID {id_val}: 数据解析异常 - {str(e)}")

    return row_data

def request_data_enhanced():
    """增强版的数据请求函数"""
    logger.info("开始增强版数据采集")

    # 初始化组件
    proxy_manager = ProxyManager()
    requester = EnhancedRequester(proxy_manager)

    # 获取ID列表和pxvid队列
    ids = get_id()
    vid_queue = load_pxvid_queue()

    all_results = []

    # 使用线程池处理
    with ThreadPoolExecutor(max_workers=50) as executor:  # 减少并发数以避免过度请求
        futures = [executor.submit(process_id_enhanced, id_val, vid_queue, requester) for id_val in ids]

        for future in concurrent.futures.as_completed(futures):
            try:
                result = future.result()
                all_results.append(result)
                logger.info(f"已完成ID {result['ID']} 的数据采集")
            except Exception as e:
                logger.error(f"处理任务时出错: {str(e)}")

    # 生成结果文件
    base_columns = ['ID', 'Brand', 'Title', 'LongDescription', 'ProductDetails', 'Link', 'zy', 'price', 'left', 'wfs', 'yunfei', 'pingfen', 'pinglun']
    image_columns = sorted(set(key for result in all_results for key in result if key.startswith('Image_')))
    columns = base_columns + image_columns

    df = pd.DataFrame(all_results).reindex(columns=columns)
    output_filename = f'enhanced_result-{datetime.today().month}-{datetime.today().day}-{name}'
    df.to_excel(output_filename, index=False)
    logger.info(f"结果已保存到 {output_filename}")

    # 统计信息
    success_count = len([r for r in all_results if r.get('Brand') and r['Brand'] not in ['请求失败', 'ID失效或缺货', 'No pxvid available']])
    logger.info(f"采集统计: 总数 {len(all_results)}, 成功 {success_count}, 成功率 {success_count/len(all_results)*100:.1f}%")

if __name__ == '__main__':
    start_time = time.time()
    try:
        request_data_enhanced()
    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}")
    finally:
        end_time = time.time()
        logger.info(f"程序执行完成，总用时: {end_time - start_time:.2f} 秒")

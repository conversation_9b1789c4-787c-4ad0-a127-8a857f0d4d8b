# coding: utf-8
# 使用示例：如何使用增强版爬虫
# File: usage_example.py

from enhanced_crawler import (
    ProxyManager, 
    AntiCrawlerHandler, 
    EnhancedRequester,
    request_data_enhanced,
    process_id_enhanced,
    load_pxvid_queue
)
import logging
import queue

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def example_single_request():
    """示例：单个产品请求"""
    logger.info("=== 单个产品请求示例 ===")
    
    try:
        # 初始化组件
        proxy_manager = ProxyManager()
        requester = EnhancedRequester(proxy_manager)
        
        # 创建一个简单的pxvid队列用于测试
        vid_queue = queue.Queue()
        vid_queue.put("test_pxvid_12345")  # 这里应该使用真实的pxvid
        
        # 测试一个产品ID
        test_id = "123456789"  # 替换为真实的Walmart产品ID
        
        logger.info(f"开始请求产品ID: {test_id}")
        result = process_id_enhanced(test_id, vid_queue, requester)
        
        logger.info("请求结果:")
        for key, value in result.items():
            logger.info(f"  {key}: {value}")
            
    except Exception as e:
        logger.error(f"单个请求示例失败: {str(e)}")

def example_proxy_rotation():
    """示例：代理轮换测试"""
    logger.info("\n=== 代理轮换示例 ===")
    
    try:
        proxy_manager = ProxyManager()
        
        logger.info("测试代理轮换:")
        for i in range(5):
            proxy = proxy_manager.get_proxy()
            if proxy:
                # 从代理URL中提取主机信息用于显示
                proxy_url = proxy.get('http', '')
                if '@' in proxy_url:
                    host_info = proxy_url.split('@')[1].split('/')[0]
                    logger.info(f"  代理 {i+1}: {host_info}")
                else:
                    logger.info(f"  代理 {i+1}: {proxy_url}")
            else:
                logger.warning(f"  代理 {i+1}: 无可用代理")
                
    except Exception as e:
        logger.error(f"代理轮换示例失败: {str(e)}")

def example_anti_crawler_headers():
    """示例：反爬虫请求头生成"""
    logger.info("\n=== 反爬虫请求头示例 ===")
    
    try:
        logger.info("生成3组不同的请求头:")
        for i in range(3):
            headers = AntiCrawlerHandler.get_enhanced_headers()
            logger.info(f"\n请求头组 {i+1}:")
            
            # 显示关键头部
            key_headers = ['User-Agent', 'Accept-Language', 'Sec-Ch-Ua-Platform']
            for header in key_headers:
                if header in headers:
                    logger.info(f"  {header}: {headers[header]}")
            
            # 显示随机指纹
            fingerprint_headers = [k for k in headers.keys() if k.startswith('X-')]
            if fingerprint_headers:
                logger.info(f"  随机指纹: {fingerprint_headers[0]} = {headers[fingerprint_headers[0]]}")
                
    except Exception as e:
        logger.error(f"请求头示例失败: {str(e)}")

def example_full_crawl():
    """示例：完整爬取流程"""
    logger.info("\n=== 完整爬取流程示例 ===")
    
    try:
        logger.info("注意：这将执行完整的爬取流程")
        logger.info("请确保以下文件存在:")
        logger.info("  - 能跑多少跑多少1.xlsx (包含ID列)")
        logger.info("  - 令牌.xlsx (包含pxvid列)")
        logger.info("  - proxies.txt (代理配置文件)")
        
        # 询问用户是否继续
        user_input = input("\n是否继续执行完整爬取? (y/N): ").strip().lower()
        
        if user_input == 'y':
            logger.info("开始执行完整爬取...")
            request_data_enhanced()
        else:
            logger.info("跳过完整爬取")
            
    except Exception as e:
        logger.error(f"完整爬取示例失败: {str(e)}")

def show_improvements():
    """显示改进点"""
    logger.info("\n=== 增强版爬虫的改进点 ===")
    
    improvements = [
        "✅ 代理池管理：支持多个SOCKS5代理轮换使用",
        "✅ 代理健康检查：自动检测和标记失效代理",
        "✅ 反爬虫检测：识别PerimeterX、Cloudflare等反爬虫系统",
        "✅ 智能重试：根据不同错误类型采用不同重试策略",
        "✅ 请求头随机化：动态生成User-Agent和其他头部",
        "✅ 指纹随机化：添加随机请求指纹避免检测",
        "✅ 连接池优化：使用Session和连接复用提高性能",
        "✅ 详细日志：完整的请求和错误日志记录",
        "✅ 统计信息：提供采集成功率等统计数据",
        "✅ 异常处理：更完善的错误处理和恢复机制"
    ]
    
    for improvement in improvements:
        logger.info(improvement)

def show_usage_tips():
    """显示使用建议"""
    logger.info("\n=== 使用建议 ===")
    
    tips = [
        "🔧 配置文件检查：",
        "   - 确保proxies.txt格式正确",
        "   - 验证令牌.xlsx中的pxvid有效性",
        "   - 检查能跑多少跑多少1.xlsx中的ID格式",
        "",
        "⚡ 性能优化：",
        "   - 根据代理质量调整线程数（建议30-50）",
        "   - 监控代理使用情况，及时更换失效代理",
        "   - 适当调整请求延时避免触发限制",
        "",
        "🛡️ 反爬虫对策：",
        "   - 定期更新pxvid令牌",
        "   - 监控成功率，低于80%时检查反爬虫策略",
        "   - 使用高质量住宅代理提高成功率",
        "",
        "📊 监控建议：",
        "   - 关注日志中的错误信息",
        "   - 定期检查输出文件的数据质量",
        "   - 监控代理池的健康状态"
    ]
    
    for tip in tips:
        logger.info(tip)

if __name__ == "__main__":
    logger.info("🚀 增强版Walmart爬虫使用示例")
    
    # 显示改进点
    show_improvements()
    
    # 显示使用建议
    show_usage_tips()
    
    # 运行示例
    try:
        # 代理轮换示例
        example_proxy_rotation()
        
        # 请求头生成示例
        example_anti_crawler_headers()
        
        # 单个请求示例（需要真实数据时取消注释）
        # example_single_request()
        
        # 完整爬取示例
        example_full_crawl()
        
    except KeyboardInterrupt:
        logger.info("\n用户中断程序")
    except Exception as e:
        logger.error(f"示例执行出错: {str(e)}")
    
    logger.info("\n示例程序结束")

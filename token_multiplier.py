# coding: utf-8
# 令牌繁殖器 - 基于成功令牌繁殖新的验证令牌
# File: token_multiplier.py

import requests
import pandas as pd
import random
import time
import queue
import threading
from datetime import datetime
from simple_proxy_crawler import SimpleProxyManager, Headers, load_pxvid_queue

class TokenMultiplier:
    """令牌繁殖器 - 通过成功请求繁殖新令牌"""
    
    def __init__(self):
        self.proxy_manager = SimpleProxyManager()
        self.working_tokens = set()
        self.new_tokens = set()
        self.lock = threading.Lock()
        self.stats = {
            'tests_performed': 0,
            'successful_tests': 0,
            'tokens_generated': 0
        }
    
    def test_and_multiply_token(self, base_token, product_id):
        """测试令牌并尝试繁殖新令牌"""
        try:
            proxy_config = self.proxy_manager.get_proxy()
            url = f"https://www.walmart.com/ip/{product_id}"
            
            # 使用基础令牌进行请求
            cookie = {"_pxvid": base_token}
            
            response = requests.get(
                url,
                cookies=cookie,
                headers=Headers(),
                proxies=proxy_config,
                timeout=15,
                verify=False
            )
            
            with self.lock:
                self.stats['tests_performed'] += 1
            
            # 判断请求是否成功
            success = (response.status_code == 200 and 
                      response.headers.get("Accept-CH") == 'Downlink, DPR')
            
            if success:
                with self.lock:
                    self.stats['successful_tests'] += 1
                    self.working_tokens.add(base_token)
                
                print(f"✅ 令牌 {base_token[:15]}... 在产品 {product_id} 上成功")
                
                # 基于成功的令牌生成新令牌
                new_tokens = self.generate_similar_tokens(base_token, count=5)
                
                with self.lock:
                    for new_token in new_tokens:
                        self.new_tokens.add(new_token)
                        self.stats['tokens_generated'] += 1
                
                print(f"   🧬 基于成功令牌生成了 {len(new_tokens)} 个新令牌")
                return True, new_tokens
            else:
                print(f"❌ 令牌 {base_token[:15]}... 在产品 {product_id} 上失败 (状态码: {response.status_code})")
                return False, []
                
        except Exception as e:
            print(f"❌ 测试令牌 {base_token[:15]}... 时出错: {str(e)}")
            return False, []
    
    def generate_similar_tokens(self, base_token, count=5):
        """基于成功令牌生成相似的新令牌"""
        new_tokens = []
        
        # 分析基础令牌的格式
        if '-' in base_token and len(base_token) == 36:
            # UUID格式
            parts = base_token.split('-')
            if len(parts) == 5:
                for _ in range(count):
                    new_parts = []
                    for i, part in enumerate(parts):
                        if i == 2 and part == '11f0':
                            # 保持第三段为11f0（关键特征）
                            new_parts.append(part)
                        elif i == 1:
                            # 第二段可能包含时间信息，稍微修改
                            base_val = int(part, 16)
                            new_val = base_val + random.randint(-100, 100)
                            new_val = max(0, min(0xFFFF, new_val))  # 确保在4位十六进制范围内
                            new_parts.append(f"{new_val:04x}")
                        else:
                            # 其他段随机生成
                            new_part = ''.join(random.choice('0123456789abcdef') for _ in range(len(part)))
                            new_parts.append(new_part)
                    
                    new_token = '-'.join(new_parts)
                    new_tokens.append(new_token)
        else:
            # 其他格式，生成相似长度的令牌
            for _ in range(count):
                new_token = ''.join(random.choice('0123456789abcdef') for _ in range(len(base_token)))
                new_tokens.append(new_token)
        
        return new_tokens
    
    def multiply_tokens_batch(self, seed_tokens, product_ids, max_iterations=3):
        """批量繁殖令牌"""
        print(f"🧬 开始令牌繁殖过程")
        print(f"🔑 种子令牌数量: {len(seed_tokens)}")
        print(f"📦 测试产品数量: {len(product_ids)}")
        print(f"🔄 最大迭代次数: {max_iterations}")
        
        current_tokens = set(seed_tokens)
        
        for iteration in range(max_iterations):
            print(f"\n🔄 第 {iteration + 1} 轮繁殖")
            print("-" * 40)
            
            iteration_new_tokens = set()
            
            # 测试当前所有令牌
            for i, token in enumerate(list(current_tokens)):
                if i >= 20:  # 限制每轮测试的令牌数量
                    break
                
                # 随机选择一个产品进行测试
                product_id = random.choice(product_ids)
                
                print(f"🔍 测试令牌 {i+1}: {token[:15]}... 在产品 {product_id}")
                
                success, new_tokens = self.test_and_multiply_token(token, product_id)
                
                if success and new_tokens:
                    iteration_new_tokens.update(new_tokens)
                
                # 添加延时避免过于激进
                time.sleep(random.uniform(2, 5))
            
            # 将新令牌添加到下一轮测试中
            if iteration_new_tokens:
                current_tokens.update(iteration_new_tokens)
                print(f"✅ 第 {iteration + 1} 轮生成了 {len(iteration_new_tokens)} 个新令牌")
            else:
                print(f"❌ 第 {iteration + 1} 轮没有生成新令牌")
                break
        
        print(f"\n📊 繁殖过程完成:")
        print(f"   - 测试次数: {self.stats['tests_performed']}")
        print(f"   - 成功次数: {self.stats['successful_tests']}")
        print(f"   - 工作令牌: {len(self.working_tokens)}")
        print(f"   - 生成令牌: {len(self.new_tokens)}")
    
    def validate_multiplied_tokens(self, sample_size=10):
        """验证繁殖的令牌"""
        print(f"\n🔍 验证繁殖的令牌")
        
        if not self.new_tokens:
            print("❌ 没有新令牌可验证")
            return 0
        
        tokens_to_test = random.sample(list(self.new_tokens), min(sample_size, len(self.new_tokens)))
        validated_count = 0
        
        for i, token in enumerate(tokens_to_test):
            try:
                proxy_config = self.proxy_manager.get_proxy()
                url = "https://www.walmart.com/ip/5083982424"  # 固定测试产品
                cookie = {"_pxvid": token}
                
                response = requests.get(
                    url,
                    cookies=cookie,
                    headers=Headers(),
                    proxies=proxy_config,
                    timeout=10,
                    verify=False
                )
                
                success = (response.status_code == 200 and 
                          response.headers.get("Accept-CH") == 'Downlink, DPR')
                
                if success:
                    validated_count += 1
                    print(f"   ✅ 新令牌 {i+1}/{len(tokens_to_test)}: 验证成功")
                else:
                    print(f"   ❌ 新令牌 {i+1}/{len(tokens_to_test)}: 验证失败")
                
                time.sleep(1)
                
            except Exception as e:
                print(f"   ❌ 新令牌 {i+1}/{len(tokens_to_test)}: 验证异常 - {str(e)}")
        
        validation_rate = validated_count / len(tokens_to_test) * 100 if tokens_to_test else 0
        print(f"📊 验证结果: {validated_count}/{len(tokens_to_test)} ({validation_rate:.1f}%)")
        
        return validation_rate
    
    def save_multiplied_tokens(self, include_originals=True):
        """保存繁殖的令牌"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"multiplied_tokens_{timestamp}.xlsx"
        
        # 决定保存哪些令牌
        if include_originals:
            all_tokens = list(self.working_tokens.union(self.new_tokens))
            print(f"💾 保存工作令牌和新令牌: {len(all_tokens)} 个")
        else:
            all_tokens = list(self.new_tokens)
            print(f"💾 仅保存新令牌: {len(all_tokens)} 个")
        
        if not all_tokens:
            print("❌ 没有令牌可保存")
            return None
        
        # 创建DataFrame并保存
        df = pd.DataFrame({'pxvid': all_tokens})
        df.to_excel(filename, index=False)
        
        print(f"✅ 令牌已保存到: {filename}")
        return filename
    
    def run_multiplication_cycle(self):
        """运行完整的令牌繁殖周期"""
        print("🧬 令牌繁殖器")
        print("="*60)
        print("通过测试已知令牌的成功性来繁殖新的验证令牌")
        print("="*60)
        
        # 1. 加载种子令牌
        try:
            vid_queue = load_pxvid_queue()
            seed_tokens = []
            for _ in range(30):  # 使用30个种子令牌
                try:
                    seed_tokens.append(vid_queue.get_nowait())
                except queue.Empty:
                    break
            print(f"🔑 加载了 {len(seed_tokens)} 个种子令牌")
        except Exception as e:
            print(f"❌ 加载种子令牌失败: {str(e)}")
            return
        
        # 2. 准备测试产品
        product_ids = [
            "5083982424",  # 已知存在的产品
            "1363194395",
            "1649269851",
            "2834567890",
            "1234567890"
        ]
        
        # 3. 执行繁殖
        self.multiply_tokens_batch(seed_tokens, product_ids, max_iterations=2)
        
        # 4. 验证新令牌
        if self.new_tokens:
            validation_rate = self.validate_multiplied_tokens(sample_size=8)
        else:
            validation_rate = 0
        
        # 5. 保存结果
        filename = self.save_multiplied_tokens(include_originals=True)
        
        # 6. 最终报告
        print(f"\n🎉 令牌繁殖周期完成!")
        print(f"📊 最终统计:")
        print(f"   - 测试次数: {self.stats['tests_performed']}")
        print(f"   - 成功次数: {self.stats['successful_tests']}")
        print(f"   - 成功率: {self.stats['successful_tests']/max(1,self.stats['tests_performed'])*100:.1f}%")
        print(f"   - 工作令牌: {len(self.working_tokens)}")
        print(f"   - 新生成令牌: {len(self.new_tokens)}")
        print(f"   - 新令牌验证率: {validation_rate:.1f}%")
        print(f"   - 保存文件: {filename}")
        
        if validation_rate > 0:
            print(f"\n🎉 成功！生成了可用的新令牌")
            print(f"💡 使用建议:")
            print(f"   1. 新令牌基于成功的原始令牌生成")
            print(f"   2. 保持了原始令牌的关键特征")
            print(f"   3. 可以直接用于爬虫程序")
        else:
            print(f"\n⚠️  虽然生成了新令牌，但验证率较低")
            print(f"💡 建议:")
            print(f"   1. 与原始令牌混合使用")
            print(f"   2. 优先使用验证成功的工作令牌")
        
        return filename

def main():
    multiplier = TokenMultiplier()
    multiplier.run_multiplication_cycle()

if __name__ == '__main__':
    main()

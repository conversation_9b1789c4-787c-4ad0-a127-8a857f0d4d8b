# coding: utf-8
# 令牌收集测试 - 检查成功请求时是否返回新令牌
# File: token_collector_test.py

import requests
import pandas as pd
import random
import time
import hashlib
import queue
from simple_proxy_crawler import SimpleProxyManager, Headers, load_pxvid_queue

def test_token_collection():
    """测试成功请求时是否会返回新令牌"""
    print("🔍 测试令牌收集机制")
    print("="*60)
    
    # 初始化
    proxy_manager = SimpleProxyManager()
    vid_queue = load_pxvid_queue()
    
    # 获取一个已知有效的令牌
    try:
        original_token = vid_queue.get_nowait()
        print(f"🔑 使用原始令牌: {original_token}")
    except queue.Empty:
        print("❌ 无可用令牌")
        return
    
    # 测试产品ID
    test_ids = ["5083982424", "1363194395", "1649269851"]
    
    for product_id in test_ids:
        print(f"\n📦 测试产品ID: {product_id}")
        print("-" * 40)
        
        # 获取代理
        proxy_config = proxy_manager.get_proxy()
        
        try:
            url = f"https://www.walmart.com/ip/{product_id}"
            cookie = {"_pxvid": original_token}
            
            print(f"🌐 请求URL: {url}")
            print(f"🍪 使用Cookie: _pxvid={original_token[:15]}...")
            print(f"🔗 使用代理: {proxy_config['info']}")
            
            response = requests.get(
                url,
                cookies=cookie,
                headers=Headers(),
                proxies=proxy_config,
                timeout=15,
                verify=False
            )
            
            print(f"📊 响应状态码: {response.status_code}")
            print(f"📋 Accept-CH头: {response.headers.get('Accept-CH', 'None')}")
            
            # 检查响应中的新令牌
            print(f"\n🔍 检查响应中的令牌信息:")
            
            # 1. 检查Set-Cookie头
            set_cookies = response.headers.get('Set-Cookie', '')
            if set_cookies:
                print(f"🍪 Set-Cookie头: {set_cookies}")
                
                # 解析Set-Cookie中的令牌
                if '_pxvid' in set_cookies:
                    print("✅ 发现Set-Cookie中包含_pxvid")
                if '_pxhd' in set_cookies:
                    print("✅ 发现Set-Cookie中包含_pxhd")
                if '_px3' in set_cookies:
                    print("✅ 发现Set-Cookie中包含_px3")
            else:
                print("❌ 无Set-Cookie头")
            
            # 2. 检查响应cookies
            response_cookies = dict(response.cookies)
            print(f"🍪 响应Cookies: {response_cookies}")
            
            if response_cookies:
                for cookie_name, cookie_value in response_cookies.items():
                    if cookie_name in ['_pxvid', '_pxhd', '_px3']:
                        print(f"✅ 发现新令牌 {cookie_name}: {cookie_value}")
                        
                        # 如果是_pxhd，提取实际令牌
                        if cookie_name == '_pxhd' and ':' in cookie_value:
                            extracted_token = cookie_value.split(':')[-1]
                            print(f"   提取的令牌: {extracted_token}")
            
            # 3. 检查响应体中的令牌
            html = response.text
            print(f"📄 响应长度: {len(html)} 字符")
            
            # 搜索可能的令牌模式
            import re
            
            # 搜索UUID格式的令牌
            uuid_pattern = r'[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
            found_uuids = re.findall(uuid_pattern, html)
            
            if found_uuids:
                print(f"🔍 在响应体中发现 {len(found_uuids)} 个UUID格式令牌:")
                for i, uuid_token in enumerate(found_uuids[:5]):  # 只显示前5个
                    print(f"   {i+1}. {uuid_token}")
                    
                    # 检查是否是新令牌（不同于原始令牌）
                    if uuid_token != original_token:
                        print(f"      ✅ 这是一个新令牌！")
            else:
                print("❌ 响应体中未发现UUID格式令牌")
            
            # 4. 搜索特定的令牌相关字段
            token_keywords = ['_pxvid', '_pxhd', '_px3', 'pxvid', 'pxhd']
            for keyword in token_keywords:
                if keyword in html:
                    print(f"🔍 响应体中包含关键词: {keyword}")
                    
                    # 尝试提取该关键词附近的值
                    pattern = rf'{keyword}["\']?\s*[:=]\s*["\']?([^"\'>\s,}}]+)'
                    matches = re.findall(pattern, html)
                    if matches:
                        print(f"   找到值: {matches[:3]}")  # 只显示前3个
            
            # 判断请求是否成功
            success = response.status_code == 200 and response.headers.get("Accept-CH") == 'Downlink, DPR'
            print(f"\n✅ 请求成功: {success}")
            
            if success:
                print("🎉 这是一个成功的请求，可以用来收集新令牌！")
            else:
                print("❌ 请求失败，无法收集新令牌")
            
            time.sleep(2)  # 避免请求过快
            
        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")
    
    print(f"\n📋 测试总结:")
    print(f"   - 测试了 {len(test_ids)} 个产品")
    print(f"   - 检查了Set-Cookie头、响应Cookies、响应体")
    print(f"   - 寻找了新的令牌信息")

def analyze_token_generation_pattern():
    """分析令牌生成模式"""
    print(f"\n🔬 分析令牌生成模式")
    print("="*60)
    
    # 加载原始令牌进行分析
    try:
        df = pd.read_excel('令牌.xlsx')
        tokens = df['pxvid'].tolist()
        
        print(f"📊 原始令牌统计:")
        print(f"   - 总数: {len(tokens)}")
        print(f"   - 唯一数: {len(set(tokens))}")
        
        # 分析令牌的时间戳特征
        print(f"\n🕐 分析令牌的时间特征:")
        
        # 提取第二段（可能包含时间信息）
        second_parts = [token.split('-')[1] for token in tokens[:100]]
        print(f"   第二段示例: {second_parts[:10]}")
        
        # 转换为十进制看是否有时间模式
        second_decimals = [int(part, 16) for part in second_parts]
        print(f"   十进制值范围: {min(second_decimals)} - {max(second_decimals)}")
        
        # 检查是否有递增模式
        sorted_decimals = sorted(second_decimals)
        if sorted_decimals == second_decimals:
            print("   ✅ 第二段呈递增模式，可能包含时间戳")
        else:
            print("   ❌ 第二段无明显递增模式")
        
        # 分析第一段和最后一段的随机性
        first_parts = [token.split('-')[0] for token in tokens[:100]]
        last_parts = [token.split('-')[4] for token in tokens[:100]]
        
        print(f"   第一段唯一数: {len(set(first_parts))}/100")
        print(f"   最后一段唯一数: {len(set(last_parts))}/100")
        
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")

if __name__ == '__main__':
    test_token_collection()
    analyze_token_generation_pattern()

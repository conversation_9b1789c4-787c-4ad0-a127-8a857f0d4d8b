# 令牌生成器使用说明

## 📋 概述

本项目提供了两个令牌生成器，用于生成类似 `令牌.xlsx` 格式的PerimeterX令牌：

1. **基础令牌生成器** (`token_generator.py`) - 适合小批量生成和学习使用
2. **高级令牌生成器** (`advanced_token_generator.py`) - 适合大批量生成和生产使用

## 🔍 原始令牌分析

### 令牌格式
- **标准格式**: `xxxxxxxx-xxxx-11f0-xxxx-xxxxxxxxxxxx`
- **分段结构**: 8位-4位-4位-4位-12位
- **第三段固定**: `11f0` (关键特征)
- **字符集**: 十六进制 (0-9, a-f)

### 原始文件特征
```
📊 令牌.xlsx 分析:
- 总数量: 54,807
- 唯一数量: 54,807  
- 重复次数: 1.0 (无重复)
- 格式: UUID变体，第三段固定为11f0
```

## 🛠️ 基础令牌生成器

### 功能特点
- 支持三种生成方法：UUID、自定义、相似
- 自动分析原始令牌格式
- 支持令牌重复和去重
- 提供命令行和交互模式

### 使用方法

#### 命令行模式
```bash
# 生成1000个令牌，每个重复5次
python token_generator.py --count 1000 --repeat 5 --method similar

# 分析原始文件并生成
python token_generator.py --analyze --count 500 --output my_tokens.xlsx
```

#### 交互模式
```bash
python token_generator.py
```

#### 编程调用
```python
from token_generator import TokenGenerator

generator = TokenGenerator()
tokens = generator.generate_tokens(count=1000, method='similar', base_token='b4a2d149-1a61-11f0-b3eb-a7beca6a8ba1')
df = generator.create_token_dataframe(tokens, repeat_count=5)
generator.save_to_excel(df, 'my_tokens.xlsx')
```

## 🚀 高级令牌生成器

### 功能特点
- 并行生成，支持大批量处理
- 自动性能优化
- 支持与原始令牌合并
- 详细的统计信息和进度显示

### 使用方法

#### 命令行模式
```bash
# 生成50,000个令牌，每个重复5次
python advanced_token_generator.py --count 50000 --repeat 5

# 生成并与原始令牌合并
python advanced_token_generator.py --count 10000 --repeat 5 --merge
```

#### 交互模式
```bash
python advanced_token_generator.py
```

#### 预设方案
1. **小批量**: 1,000 × 5 = 5,000 令牌
2. **中批量**: 10,000 × 5 = 50,000 令牌  
3. **大批量**: 50,000 × 5 = 250,000 令牌
4. **超大批量**: 100,000 × 5 = 500,000 令牌

## 📊 性能对比

| 生成器类型 | 适用场景 | 生成速度 | 内存占用 | 并发支持 |
|-----------|---------|---------|---------|---------|
| 基础生成器 | 小批量(<10K) | ~1000/秒 | 低 | 否 |
| 高级生成器 | 大批量(>10K) | ~50000/秒 | 中等 | 是 |

## ⚠️ 重要说明

### 令牌有效性
1. **原始令牌优势**: 
   - 来自真实用户会话
   - 具有"预验证"状态
   - 对PerimeterX系统更友好

2. **生成令牌限制**:
   - 格式正确但缺乏验证历史
   - 可能触发更严格的检测
   - 建议与原始令牌混合使用

### 使用建议
1. **混合策略**: 70%原始令牌 + 30%生成令牌
2. **批量测试**: 先小批量测试有效性
3. **格式保持**: 确保第三段为`11f0`
4. **随机分布**: 避免连续使用相似令牌

## 🔧 技术细节

### 令牌生成算法
```python
def generate_similar_token(base_token):
    parts = base_token.split('-')
    # 保持第三段不变 (11f0)
    # 随机化其他段
    new_parts = [
        random_hex(8),    # 第一段
        random_hex(4),    # 第二段  
        parts[2],         # 第三段 (固定)
        random_hex(4),    # 第四段
        random_hex(12)    # 第五段
    ]
    return '-'.join(new_parts)
```

### 并行优化
- 使用ThreadPoolExecutor进行并行生成
- 自动分配任务到多个线程
- 支持8个并发线程以获得最佳性能

## 📁 文件结构

```
令牌生成器/
├── token_generator.py              # 基础生成器
├── advanced_token_generator.py     # 高级生成器
├── token_generator_usage.py        # 使用示例
├── 令牌生成器说明.md               # 本说明文档
├── 令牌.xlsx                      # 原始令牌文件
└── generated_tokens_*.xlsx         # 生成的令牌文件
```

## 🎯 使用示例

### 快速开始
```bash
# 1. 生成测试令牌
python token_generator.py --count 100

# 2. 生成大批量令牌
python advanced_token_generator.py --count 10000 --repeat 5

# 3. 运行完整演示
python token_generator_usage.py
```

### 集成到爬虫
```python
import pandas as pd
import queue

# 加载生成的令牌
df = pd.read_excel('generated_tokens_20250729_020313.xlsx')
vid_queue = queue.Queue()

for token in df['pxvid']:
    vid_queue.put(token)

# 在爬虫中使用
def process_product(product_id):
    try:
        pxvid = vid_queue.get_nowait()
        cookie = {"_pxvid": pxvid}
        # 使用cookie进行请求...
    except queue.Empty:
        print("令牌用完")
```

## 🔍 故障排除

### 常见问题
1. **生成速度慢**: 使用高级生成器的并行模式
2. **内存不足**: 减少批量大小或增加系统内存
3. **格式错误**: 确保基础令牌格式正确
4. **文件过大**: 使用分批生成和保存

### 调试技巧
```python
# 验证令牌格式
def validate_token(token):
    parts = token.split('-')
    if len(parts) != 5:
        return False
    lengths = [len(part) for part in parts]
    return lengths == [8,4,4,4,12] and parts[2] == '11f0'
```

## 📈 未来改进

1. **智能生成**: 基于成功率优化生成策略
2. **实时验证**: 集成令牌有效性检测
3. **分布式生成**: 支持多机器并行生成
4. **格式扩展**: 支持其他类型的令牌格式

---

**注意**: 生成的令牌仅用于学习和测试目的，请遵守相关网站的使用条款和法律法规。

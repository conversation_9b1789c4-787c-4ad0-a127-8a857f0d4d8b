# coding: utf-8
# 令牌分析工具 - 对比静态令牌和动态获取令牌的区别
# File: token_analysis.py

import requests
import pandas as pd
import random
import time
import hashlib
import json
from datetime import datetime

def md5_encrypt(string):
    """MD5加密"""
    md5 = hashlib.md5()
    md5.update(string.encode('utf-8'))
    return md5.hexdigest()

def Headers():
    """生成随机请求头"""
    v1 = random.randint(100, 135)
    v2 = random.randint(10, 25)
    v3 = random.randint(400, 600)

    headers = {
        'name': '',
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Ch-Ua-Platform": "\"Windows\"",
        "Sec-Fetch-User": "?1",
        "Priority": "u=0, i",
        'Sec-Ch-Ua': f'"Microsoft Edge";v="{v1}", "Not-A.Brand";v="{v2}", "Chromium";v="{v1}"',
        f"F{md5_encrypt(str(time.time()))[:5]}": f"{md5_encrypt(str(random.randint(1, 10000)))}",
        "User-Agent": f"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/{v3}.36 (KHTML, like Gecko) Chrome/{v1}.0.0.0 Safari/{v3}.36 Edg/{v1}.0.0.0",
    }
    return headers

def get_proxy():
    """获取代理配置"""
    proxyAddr = "overseas.tunnel.qg.net:15655"
    authKey = "0MQGW8CU"
    password = "99AF4C18800B"
    proxyUrl = "http://%(user)s:%(password)s@%(server)s" % {
        "user": authKey,
        "password": password,
        "server": proxyAddr,
    }
    return {
        "http": proxyUrl,
        "https": proxyUrl,
        "info": proxyAddr
    }

def get_static_token():
    """从令牌.xlsx获取静态令牌"""
    try:
        df = pd.read_excel('令牌.xlsx')
        pxvid_list = df['pxvid'].dropna().astype(str).tolist()
        if pxvid_list:
            token = pxvid_list[0]  # 取第一个令牌
            print(f"📋 静态令牌: {token}")
            return token
    except Exception as e:
        print(f"❌ 获取静态令牌失败: {str(e)}")
    return None

def get_dynamic_token():
    """动态获取令牌（参考队列.py）"""
    print("🔄 尝试动态获取令牌...")
    url = f"https://www.walmart.com/ip/{random.randint(1, 10000)}{str(time.time())}{str(random.randint(1, 10000))}{str(time.time())}{str(random.randint(1, 10000))}"
    
    try:
        response = requests.head(
            url, 
            headers=Headers(), 
            proxies=get_proxy(), 
            verify=False, 
            timeout=15
        )
        
        print(f"🌐 动态获取URL: {url}")
        print(f"📊 响应状态码: {response.status_code}")
        print(f"🍪 响应Cookies: {dict(response.cookies)}")
        print(f"📋 响应Headers: {dict(response.headers)}")
        
        # 尝试从cookies中获取_pxhd
        if '_pxhd' in response.cookies:
            full_pxhd = response.cookies.get('_pxhd')
            _pxvid = full_pxhd.split(':')[-1]  # 按队列.py的方式处理
            print(f"✅ 完整_pxhd: {full_pxhd}")
            print(f"✅ 提取的_pxvid: {_pxvid}")
            return _pxvid, full_pxhd
        
        # 检查其他可能的令牌
        for cookie_name in ['_pxvid', '_px3']:
            if cookie_name in response.cookies:
                token = response.cookies.get(cookie_name)
                print(f"✅ 找到 {cookie_name}: {token}")
                return token, token
        
        print("❌ 未找到任何令牌")
        return None, None
        
    except Exception as e:
        print(f"❌ 动态获取令牌失败: {str(e)}")
        return None, None

def test_token_with_product(token, token_type, product_id="15351062232"):
    """使用令牌测试产品请求"""
    print(f"\n🧪 测试{token_type}令牌: {token[:20]}...")
    
    url = f"https://www.walmart.com/ip/{product_id}"
    cookie = {"_pxvid": token}
    
    try:
        response = requests.get(
            url,
            cookies=cookie,
            headers=Headers(),
            proxies=get_proxy(),
            timeout=15,
            verify=False
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📋 Accept-CH头: {response.headers.get('Accept-CH', 'None')}")
        print(f"🍪 响应Cookies: {dict(response.cookies)}")
        
        # 检查关键成功指标
        success = response.status_code == 200 and response.headers.get("Accept-CH") == 'Downlink, DPR'
        print(f"✅ 成功判断: {success}")
        
        # 保存响应内容用于分析
        filename = f"response_{token_type}_{product_id}_{int(time.time())}.html"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(response.text)
        print(f"💾 响应内容已保存到: {filename}")
        
        # 分析响应内容
        html = response.text
        print(f"📄 响应长度: {len(html)} 字符")
        
        # 检查是否包含产品信息
        if '"productName"' in html:
            print("✅ 包含产品信息")
        else:
            print("❌ 不包含产品信息")
            
        # 检查是否有PerimeterX相关内容
        if 'perimeterx' in html.lower() or '_px' in html.lower():
            print("🛡️ 检测到PerimeterX相关内容")
        
        # 检查是否有验证码或阻拦页面
        if 'captcha' in html.lower() or 'blocked' in html.lower():
            print("🚫 检测到验证码或阻拦页面")
            
        # 提取页面标题
        import re
        title_match = re.search(r'<title>(.*?)</title>', html, re.IGNORECASE)
        if title_match:
            print(f"📰 页面标题: {title_match.group(1)}")
        
        return success, response
        
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")
        return False, None

def analyze_token_differences():
    """分析令牌差异"""
    print("="*80)
    print("🔍 令牌来源和差异分析")
    print("="*80)
    
    # 1. 获取静态令牌
    print("\n1️⃣ 获取静态令牌")
    print("-" * 40)
    static_token = get_static_token()
    
    # 2. 获取动态令牌
    print("\n2️⃣ 获取动态令牌")
    print("-" * 40)
    dynamic_token, full_pxhd = get_dynamic_token()
    
    # 3. 对比令牌格式
    print("\n3️⃣ 令牌格式对比")
    print("-" * 40)
    if static_token:
        print(f"静态令牌长度: {len(static_token)}")
        print(f"静态令牌格式: {static_token}")
        print(f"静态令牌是否包含'-': {'-' in static_token}")
        print(f"静态令牌是否包含':': {':' in static_token}")
    
    if dynamic_token:
        print(f"动态令牌长度: {len(dynamic_token)}")
        print(f"动态令牌格式: {dynamic_token}")
        print(f"动态令牌是否包含'-': {'-' in dynamic_token}")
        print(f"动态令牌是否包含':': {':' in dynamic_token}")
        if full_pxhd:
            print(f"完整_pxhd: {full_pxhd}")
    
    # 4. 测试令牌效果
    print("\n4️⃣ 令牌效果测试")
    print("-" * 40)
    
    if static_token:
        print("测试静态令牌:")
        static_success, static_response = test_token_with_product(static_token, "静态")
    
    if dynamic_token:
        print("\n测试动态令牌:")
        dynamic_success, dynamic_response = test_token_with_product(dynamic_token, "动态")
    
    # 5. 总结分析
    print("\n5️⃣ 分析总结")
    print("-" * 40)
    print(f"静态令牌成功: {static_success if static_token else 'N/A'}")
    print(f"动态令牌成功: {dynamic_success if dynamic_token else 'N/A'}")
    
    # 保存分析结果
    analysis_result = {
        "timestamp": datetime.now().isoformat(),
        "static_token": static_token,
        "dynamic_token": dynamic_token,
        "full_pxhd": full_pxhd,
        "static_success": static_success if static_token else None,
        "dynamic_success": dynamic_success if dynamic_token else None
    }
    
    with open(f"token_analysis_{int(time.time())}.json", 'w', encoding='utf-8') as f:
        json.dump(analysis_result, f, ensure_ascii=False, indent=2)
    
    print(f"📊 分析结果已保存到: token_analysis_{int(time.time())}.json")

if __name__ == '__main__':
    analyze_token_differences()

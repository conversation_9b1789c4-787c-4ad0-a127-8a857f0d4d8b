# coding: utf-8
# 增强版爬虫安装和配置脚本
# File: setup_and_install.py

import os
import sys
import subprocess
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_python_version():
    """检查Python版本"""
    logger.info("检查Python版本...")
    if sys.version_info < (3, 7):
        logger.error("需要Python 3.7或更高版本")
        return False
    logger.info(f"Python版本: {sys.version}")
    return True

def install_dependencies():
    """安装依赖包"""
    logger.info("开始安装依赖包...")
    
    dependencies = [
        "pandas",
        "requests",
        "urllib3",
        "openpyxl",
        "requests[socks]"
    ]
    
    for dep in dependencies:
        try:
            logger.info(f"安装 {dep}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            logger.info(f"✅ {dep} 安装成功")
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ {dep} 安装失败: {str(e)}")
            return False
    
    logger.info("所有依赖包安装完成")
    return True

def check_required_files():
    """检查必需的配置文件"""
    logger.info("检查必需的配置文件...")
    
    required_files = {
        "proxies.txt": "代理配置文件",
        "令牌.xlsx": "pxvid令牌文件",
        "能跑多少跑多少1.xlsx": "产品ID列表文件"
    }
    
    missing_files = []
    
    for filename, description in required_files.items():
        if os.path.exists(filename):
            logger.info(f"✅ {filename} ({description}) - 存在")
        else:
            logger.warning(f"❌ {filename} ({description}) - 缺失")
            missing_files.append((filename, description))
    
    if missing_files:
        logger.warning("缺失的文件:")
        for filename, description in missing_files:
            logger.warning(f"  - {filename}: {description}")
        return False
    
    return True

def validate_proxy_file():
    """验证代理文件格式"""
    logger.info("验证代理文件格式...")
    
    if not os.path.exists("proxies.txt"):
        logger.error("proxies.txt 文件不存在")
        return False
    
    try:
        with open("proxies.txt", 'r', encoding='utf-8') as f:
            content = f.read()
        
        proxy_blocks = content.strip().split('\n\n')
        valid_proxies = 0
        
        for block in proxy_blocks:
            if not block.strip():
                continue
            
            lines = block.strip().split('\n')
            required_fields = ['协议:', '地址:', '端口:', '用户名:', '密码:']
            
            found_fields = []
            for line in lines:
                for field in required_fields:
                    if field in line:
                        found_fields.append(field)
                        break
            
            if len(found_fields) == len(required_fields):
                valid_proxies += 1
        
        logger.info(f"✅ 发现 {valid_proxies} 个有效代理配置")
        return valid_proxies > 0
        
    except Exception as e:
        logger.error(f"验证代理文件时出错: {str(e)}")
        return False

def validate_excel_files():
    """验证Excel文件格式"""
    logger.info("验证Excel文件格式...")
    
    try:
        import pandas as pd
        
        # 检查令牌文件
        if os.path.exists("令牌.xlsx"):
            df_token = pd.read_excel("令牌.xlsx")
            if 'pxvid' in df_token.columns:
                pxvid_count = len(df_token['pxvid'].dropna())
                logger.info(f"✅ 令牌.xlsx - 发现 {pxvid_count} 个pxvid")
            else:
                logger.error("❌ 令牌.xlsx 缺少 'pxvid' 列")
                return False
        
        # 检查ID文件
        if os.path.exists("能跑多少跑多少1.xlsx"):
            df_ids = pd.read_excel("能跑多少跑多少1.xlsx")
            if 'ID' in df_ids.columns:
                id_count = len(df_ids['ID'].dropna())
                logger.info(f"✅ 能跑多少跑多少1.xlsx - 发现 {id_count} 个产品ID")
            else:
                logger.error("❌ 能跑多少跑多少1.xlsx 缺少 'ID' 列")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"验证Excel文件时出错: {str(e)}")
        return False

def create_sample_config():
    """创建示例配置文件"""
    logger.info("创建示例配置文件...")
    
    # 创建示例代理文件
    if not os.path.exists("proxies.txt"):
        sample_proxy = """协议: socks5
地址: 127.0.0.1
端口: 1080
用户名: username
密码: password

协议: socks5
地址: 127.0.0.1
端口: 1081
用户名: username2
密码: password2"""
        
        with open("proxies_sample.txt", 'w', encoding='utf-8') as f:
            f.write(sample_proxy)
        logger.info("✅ 创建了 proxies_sample.txt 示例文件")
    
    # 创建示例Excel文件说明
    excel_info = """
Excel文件格式说明:

1. 令牌.xlsx 需要包含以下列:
   - pxvid: PerimeterX的验证令牌

2. 能跑多少跑多少1.xlsx 需要包含以下列:
   - ID: Walmart产品ID列表

请确保这些文件存在并包含正确的数据。
"""
    
    with open("Excel文件格式说明.txt", 'w', encoding='utf-8') as f:
        f.write(excel_info)
    logger.info("✅ 创建了 Excel文件格式说明.txt")

def run_basic_test():
    """运行基础测试"""
    logger.info("运行基础功能测试...")
    
    try:
        # 测试导入
        from enhanced_crawler import ProxyManager, AntiCrawlerHandler
        logger.info("✅ 模块导入成功")
        
        # 测试代理管理器
        if os.path.exists("proxies.txt"):
            proxy_manager = ProxyManager()
            if proxy_manager.proxies:
                logger.info(f"✅ 代理管理器初始化成功，加载了 {len(proxy_manager.proxies)} 个代理")
            else:
                logger.warning("⚠️ 代理管理器初始化成功，但没有加载到代理")
        
        # 测试反爬虫处理器
        handler = AntiCrawlerHandler()
        headers = handler.get_enhanced_headers()
        if headers and 'User-Agent' in headers:
            logger.info("✅ 反爬虫处理器工作正常")
        else:
            logger.error("❌ 反爬虫处理器异常")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"基础测试失败: {str(e)}")
        return False

def main():
    """主安装流程"""
    logger.info("🚀 开始增强版Walmart爬虫安装和配置")
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 安装依赖
    if not install_dependencies():
        logger.error("依赖安装失败，请手动安装")
        return False
    
    # 检查配置文件
    files_exist = check_required_files()
    
    if not files_exist:
        logger.warning("缺少必需的配置文件")
        create_sample_config()
        logger.info("已创建示例配置文件，请根据说明配置后重新运行")
        return False
    
    # 验证配置文件
    if not validate_proxy_file():
        logger.error("代理文件格式验证失败")
        return False
    
    if not validate_excel_files():
        logger.error("Excel文件格式验证失败")
        return False
    
    # 运行基础测试
    if not run_basic_test():
        logger.error("基础功能测试失败")
        return False
    
    logger.info("🎉 安装和配置完成！")
    logger.info("现在可以运行以下命令:")
    logger.info("  python test_proxy_pool.py    # 测试代理池")
    logger.info("  python usage_example.py      # 查看使用示例")
    logger.info("  python enhanced_crawler.py   # 运行完整爬虫")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            logger.error("安装失败，请检查错误信息")
            sys.exit(1)
    except KeyboardInterrupt:
        logger.info("用户中断安装")
        sys.exit(1)
    except Exception as e:
        logger.error(f"安装过程中出现未预期的错误: {str(e)}")
        sys.exit(1)

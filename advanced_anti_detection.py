# coding: utf-8
# 高级反PerimeterX检测系统
# File: advanced_anti_detection.py

import asyncio
import random
import time
import json
import re
import logging
from typing import Dict, List, Optional, Tuple
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page
import pandas as pd
import queue
import threading
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AdvancedProxyManager:
    """高级代理管理器，支持Playwright"""
    
    def __init__(self, proxy_file: str = "proxies.txt"):
        self.proxy_file = proxy_file
        self.proxies = []
        self.current_index = 0
        self.failed_proxies = set()
        self.lock = threading.Lock()
        self.load_proxies()
    
    def load_proxies(self):
        """加载代理列表"""
        try:
            with open(self.proxy_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            proxy_blocks = content.strip().split('\n\n')
            for block in proxy_blocks:
                if not block.strip():
                    continue
                
                lines = block.strip().split('\n')
                proxy_info = {}
                
                for line in lines:
                    if '协议:' in line:
                        proxy_info['protocol'] = line.split('协议:')[1].strip()
                    elif '地址:' in line:
                        proxy_info['host'] = line.split('地址:')[1].strip()
                    elif '端口:' in line:
                        proxy_info['port'] = line.split('端口:')[1].strip()
                    elif '用户名:' in line:
                        proxy_info['username'] = line.split('用户名:')[1].strip()
                    elif '密码:' in line:
                        proxy_info['password'] = line.split('密码:')[1].strip()
                
                if all(key in proxy_info for key in ['protocol', 'host', 'port', 'username', 'password']):
                    self.proxies.append(proxy_info)
            
            logger.info(f"成功加载 {len(self.proxies)} 个代理")
            
        except Exception as e:
            logger.error(f"加载代理文件失败: {str(e)}")
            raise
    
    def get_proxy_config(self) -> Optional[Dict]:
        """获取Playwright格式的代理配置"""
        with self.lock:
            if not self.proxies:
                return None
            
            attempts = 0
            while attempts < len(self.proxies):
                proxy = self.proxies[self.current_index]
                proxy_key = f"{proxy['host']}:{proxy['port']}"
                
                if proxy_key not in self.failed_proxies:
                    proxy_config = {
                        "server": f"{proxy['protocol']}://{proxy['host']}:{proxy['port']}",
                        "username": proxy['username'],
                        "password": proxy['password']
                    }
                    
                    self.current_index = (self.current_index + 1) % len(self.proxies)
                    return proxy_config
                
                self.current_index = (self.current_index + 1) % len(self.proxies)
                attempts += 1
            
            # 如果所有代理都失效了，清空失效列表重新开始
            if len(self.failed_proxies) >= len(self.proxies):
                logger.warning("所有代理都已失效，重置失效列表")
                self.failed_proxies.clear()
                return self.get_proxy_config()
            
            return None
    
    def mark_proxy_failed(self, proxy_config: Dict):
        """标记代理为失效"""
        if not proxy_config:
            return
        
        try:
            server = proxy_config.get('server', '')
            if '://' in server:
                host_port = server.split('://')[-1]
                with self.lock:
                    self.failed_proxies.add(host_port)
                    logger.warning(f"标记代理失效: {host_port}")
        except Exception as e:
            logger.error(f"标记代理失效时出错: {str(e)}")

class AdvancedAntiDetection:
    """高级反检测系统"""
    
    @staticmethod
    async def setup_stealth_browser(browser: Browser, proxy_config: Dict = None) -> BrowserContext:
        """设置隐身浏览器上下文"""
        
        # 随机用户代理
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        ]
        
        # 随机视口大小
        viewports = [
            {"width": 1920, "height": 1080},
            {"width": 1366, "height": 768},
            {"width": 1440, "height": 900},
            {"width": 1536, "height": 864}
        ]
        
        context_options = {
            "user_agent": random.choice(user_agents),
            "viewport": random.choice(viewports),
            "locale": random.choice(["en-US", "en-GB", "zh-CN"]),
            "timezone_id": random.choice(["America/New_York", "Europe/London", "Asia/Shanghai"]),
            "permissions": ["geolocation"],
            "extra_http_headers": {
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
                "Accept-Language": "en-US,en;q=0.5",
                "Accept-Encoding": "gzip, deflate, br",
                "DNT": "1",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1",
                "Sec-Fetch-Dest": "document",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Site": "none",
                "Sec-Fetch-User": "?1"
            }
        }
        
        if proxy_config:
            context_options["proxy"] = proxy_config
        
        context = await browser.new_context(**context_options)
        
        # 注入反检测脚本
        await context.add_init_script("""
            // 覆盖webdriver属性
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            // 覆盖plugins
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            
            // 覆盖languages
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en'],
            });
            
            // 覆盖chrome属性
            window.chrome = {
                runtime: {},
            };
            
            // 覆盖permissions
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );
            
            // 随机化canvas指纹
            const getContext = HTMLCanvasElement.prototype.getContext;
            HTMLCanvasElement.prototype.getContext = function(type) {
                if (type === '2d') {
                    const context = getContext.call(this, type);
                    const originalFillText = context.fillText;
                    context.fillText = function(text, x, y, maxWidth) {
                        // 添加微小的随机偏移
                        const randomOffset = Math.random() * 0.1;
                        return originalFillText.call(this, text, x + randomOffset, y + randomOffset, maxWidth);
                    };
                    return context;
                }
                return getContext.call(this, type);
            };
        """)
        
        return context
    
    @staticmethod
    async def simulate_human_behavior(page: Page):
        """模拟人类行为"""
        try:
            # 随机鼠标移动
            for _ in range(random.randint(2, 5)):
                x = random.randint(100, 800)
                y = random.randint(100, 600)
                await page.mouse.move(x, y)
                await asyncio.sleep(random.uniform(0.1, 0.3))
            
            # 随机滚动
            for _ in range(random.randint(1, 3)):
                await page.mouse.wheel(0, random.randint(-300, 300))
                await asyncio.sleep(random.uniform(0.5, 1.0))
            
            # 随机点击（不在重要元素上）
            if random.random() > 0.7:
                x = random.randint(50, 200)
                y = random.randint(50, 200)
                await page.mouse.click(x, y)
                await asyncio.sleep(random.uniform(0.2, 0.5))
                
        except Exception as e:
            logger.debug(f"模拟人类行为时出错: {str(e)}")
    
    @staticmethod
    async def handle_perimeter_x_challenge(page: Page) -> bool:
        """处理PerimeterX挑战"""
        try:
            # 等待页面加载
            await asyncio.sleep(random.uniform(2, 4))
            
            # 检查是否有PerimeterX挑战
            px_elements = await page.query_selector_all('[id*="px"], [class*="px"], [data-px]')
            
            if px_elements:
                logger.info("检测到PerimeterX挑战，尝试处理...")
                
                # 模拟人类行为
                await AdvancedAntiDetection.simulate_human_behavior(page)
                
                # 等待挑战完成
                for attempt in range(10):
                    await asyncio.sleep(2)
                    
                    # 检查是否有验证码或其他挑战元素
                    captcha = await page.query_selector('iframe[src*="captcha"], [class*="captcha"], [id*="captcha"]')
                    if captcha:
                        logger.warning("检测到验证码，需要人工处理")
                        return False
                    
                    # 检查页面是否正常加载
                    if await page.query_selector('[data-automation-id="product-title"], .prod-ProductTitle'):
                        logger.info("PerimeterX挑战已通过")
                        return True
                
                logger.warning("PerimeterX挑战处理超时")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"处理PerimeterX挑战时出错: {str(e)}")
            return False

class AdvancedWalmartCrawler:
    """高级Walmart爬虫"""
    
    def __init__(self):
        self.proxy_manager = AdvancedProxyManager()
        self.anti_detection = AdvancedAntiDetection()
        self.browser = None
        self.semaphore = asyncio.Semaphore(10)  # 限制并发数
    
    async def init_browser(self):
        """初始化浏览器"""
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(
            headless=True,  # 设为False可以看到浏览器操作
            args=[
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding'
            ]
        )
    
    async def close_browser(self):
        """关闭浏览器"""
        if self.browser:
            await self.browser.close()
        if hasattr(self, 'playwright'):
            await self.playwright.stop()
    
    async def crawl_product(self, product_id: str, pxvid: str) -> Dict:
        """爬取单个产品"""
        async with self.semaphore:
            return await self._crawl_single_product(product_id, pxvid)
    
    async def _crawl_single_product(self, product_id: str, pxvid: str) -> Dict:
        """爬取单个产品的内部实现"""
        result = {'ID': product_id}
        
        proxy_config = self.proxy_manager.get_proxy_config()
        if not proxy_config:
            result['Brand'] = '无可用代理'
            return result
        
        context = None
        page = None
        
        try:
            # 创建浏览器上下文
            context = await self.anti_detection.setup_stealth_browser(self.browser, proxy_config)
            page = await context.new_page()
            
            # 设置cookie
            await context.add_cookies([{
                'name': '_pxvid',
                'value': pxvid,
                'domain': '.walmart.com',
                'path': '/'
            }])
            
            # 访问产品页面
            url = f"https://www.walmart.com/ip/{product_id}"
            logger.info(f"访问产品页面: {product_id}")
            
            response = await page.goto(url, wait_until='domcontentloaded', timeout=30000)
            
            if not response:
                result['Brand'] = '页面加载失败'
                return result
            
            # 处理PerimeterX挑战
            if not await self.anti_detection.handle_perimeter_x_challenge(page):
                result['Brand'] = 'PerimeterX挑战失败'
                self.proxy_manager.mark_proxy_failed(proxy_config)
                return result
            
            # 等待页面完全加载
            await asyncio.sleep(random.uniform(2, 4))
            
            # 提取产品信息
            await self.extract_product_info(page, result)
            
            logger.info(f"产品 {product_id} 爬取成功")
            
        except Exception as e:
            logger.error(f"爬取产品 {product_id} 时出错: {str(e)}")
            result['Brand'] = f'爬取失败: {str(e)}'
            if proxy_config:
                self.proxy_manager.mark_proxy_failed(proxy_config)
        
        finally:
            if page:
                await page.close()
            if context:
                await context.close()
        
        return result
    
    async def extract_product_info(self, page: Page, result: Dict):
        """提取产品信息"""
        try:
            # 等待关键元素加载
            await page.wait_for_selector('[data-automation-id="product-title"], .prod-ProductTitle', timeout=10000)
            
            # 提取品牌
            brand_selectors = [
                '[data-automation-id="product-brand"] a',
                '.prod-ProductBrand a',
                '[data-testid="product-brand"]'
            ]
            
            for selector in brand_selectors:
                try:
                    brand_element = await page.query_selector(selector)
                    if brand_element:
                        result['Brand'] = await brand_element.inner_text()
                        break
                except:
                    continue
            
            # 提取标题
            title_selectors = [
                '[data-automation-id="product-title"]',
                '.prod-ProductTitle',
                'h1[data-testid="product-title"]'
            ]
            
            for selector in title_selectors:
                try:
                    title_element = await page.query_selector(selector)
                    if title_element:
                        result['Title'] = await title_element.inner_text()
                        break
                except:
                    continue
            
            # 提取价格
            price_selectors = [
                '[data-testid="price-current"]',
                '.price-current',
                '[data-automation-id="product-price"]'
            ]
            
            for selector in price_selectors:
                try:
                    price_element = await page.query_selector(selector)
                    if price_element:
                        price_text = await price_element.inner_text()
                        # 提取数字
                        price_match = re.search(r'[\d,]+\.?\d*', price_text.replace('$', '').replace(',', ''))
                        if price_match:
                            result['price'] = price_match.group()
                        break
                except:
                    continue
            
            # 提取图片
            try:
                img_elements = await page.query_selector_all('img[data-testid="hero-image"], .prod-hero-image img')
                for idx, img in enumerate(img_elements[:10], 1):
                    src = await img.get_attribute('src')
                    if src and 'http' in src:
                        result[f'Image_{idx}'] = src
            except:
                pass
            
            # 如果没有提取到品牌，标记为失效
            if not result.get('Brand'):
                result['Brand'] = 'ID失效或缺货'
            
        except Exception as e:
            logger.error(f"提取产品信息时出错: {str(e)}")
            result['Brand'] = f'信息提取失败: {str(e)}'

# 辅助函数
def get_id():
    """获取产品ID列表"""
    name = "能跑多少跑多少1.xlsx"
    df = pd.read_excel(name)
    ids = df['ID'].astype(str).tolist()
    print("待查询ID列表:", ids)
    return ids

def load_pxvid_queue():
    """加载pxvid队列"""
    try:
        df = pd.read_excel('令牌.xlsx')
        pxvid_list = df['pxvid'].dropna().astype(str).tolist()
        extended_vids = []
        for vid in pxvid_list:
            extended_vids.extend([vid] * 5)
        random.shuffle(extended_vids)
        q = queue.Queue()
        for vid in extended_vids:
            q.put(vid)
        print(f"成功加载{len(extended_vids)}个pxvid到队列")
        return q
    except Exception as e:
        print(f"加载pxvid队列失败: {str(e)}")
        raise

async def run_advanced_crawler():
    """运行高级爬虫"""
    logger.info("🚀 启动高级反PerimeterX爬虫系统")

    # 获取数据
    ids = get_id()
    vid_queue = load_pxvid_queue()

    # 初始化爬虫
    crawler = AdvancedWalmartCrawler()
    await crawler.init_browser()

    try:
        all_results = []

        # 创建任务列表
        tasks = []
        for product_id in ids:
            try:
                pxvid = vid_queue.get_nowait()
                task = crawler.crawl_product(product_id, pxvid)
                tasks.append(task)
            except queue.Empty:
                logger.warning(f"产品 {product_id}: 无可用pxvid")
                all_results.append({'ID': product_id, 'Brand': 'No pxvid available'})

        # 批量执行任务
        batch_size = 5  # 每批处理5个产品
        for i in range(0, len(tasks), batch_size):
            batch_tasks = tasks[i:i+batch_size]
            logger.info(f"处理批次 {i//batch_size + 1}/{(len(tasks)-1)//batch_size + 1}")

            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)

            for result in batch_results:
                if isinstance(result, Exception):
                    logger.error(f"批次处理异常: {str(result)}")
                    all_results.append({'ID': 'unknown', 'Brand': f'处理异常: {str(result)}'})
                else:
                    all_results.append(result)
                    logger.info(f"已完成产品 {result['ID']} 的数据采集")

            # 批次间延时
            await asyncio.sleep(random.uniform(5, 10))

        # 保存结果
        base_columns = ['ID', 'Brand', 'Title', 'price']
        image_columns = sorted(set(key for result in all_results for key in result if key.startswith('Image_')))
        columns = base_columns + image_columns

        df = pd.DataFrame(all_results).reindex(columns=columns)
        output_filename = f'advanced_result-{datetime.today().month}-{datetime.today().day}-能跑多少跑多少1.xlsx'
        df.to_excel(output_filename, index=False)

        # 统计信息
        success_count = len([r for r in all_results if r.get('Brand') and r['Brand'] not in ['爬取失败', 'ID失效或缺货', 'No pxvid available', 'PerimeterX挑战失败']])
        logger.info(f"🎉 爬取完成！")
        logger.info(f"📊 统计信息: 总数 {len(all_results)}, 成功 {success_count}, 成功率 {success_count/len(all_results)*100:.1f}%")
        logger.info(f"📁 结果已保存到: {output_filename}")

    finally:
        await crawler.close_browser()

def run_crawler():
    """同步运行爬虫的包装函数"""
    try:
        asyncio.run(run_advanced_crawler())
    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}")

if __name__ == '__main__':
    start_time = time.time()
    try:
        run_crawler()
    finally:
        end_time = time.time()
        logger.info(f"程序执行完成，总用时: {end_time - start_time:.2f} 秒")

# coding: utf-8
# 网页内容查看器 - 对比静态令牌和动态令牌的响应内容
# File: web_content_viewer.py

import webbrowser
import os
from datetime import datetime

def create_comparison_html():
    """创建对比页面HTML"""
    
    # 读取静态令牌响应
    try:
        with open('response_静态_15351062232_1753725221.html', 'r', encoding='utf-8') as f:
            static_content = f.read()
    except:
        static_content = "无法读取静态令牌响应文件"
    
    # 读取动态令牌响应
    try:
        with open('response_动态_15351062232_1753725223.html', 'r', encoding='utf-8') as f:
            dynamic_content = f.read()
    except:
        dynamic_content = "无法读取动态令牌响应文件"
    
    # 创建对比HTML
    comparison_html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>令牌响应内容对比</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .header {{
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }}
        .comparison-container {{
            display: flex;
            gap: 20px;
            height: 80vh;
        }}
        .panel {{
            flex: 1;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .panel-header {{
            padding: 15px;
            font-weight: bold;
            font-size: 18px;
            border-bottom: 2px solid #eee;
        }}
        .success {{
            background-color: #d4edda;
            color: #155724;
            border-left: 5px solid #28a745;
        }}
        .failure {{
            background-color: #f8d7da;
            color: #721c24;
            border-left: 5px solid #dc3545;
        }}
        .content-frame {{
            width: 100%;
            height: calc(100% - 60px);
            border: none;
        }}
        .stats {{
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .stat-item {{
            text-align: center;
        }}
        .stat-value {{
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }}
        .stat-label {{
            color: #666;
            margin-top: 5px;
        }}
        .analysis {{
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-top: 20px;
        }}
        .highlight {{
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 令牌响应内容对比分析</h1>
        <p>静态令牌 vs 动态令牌 - 网页响应内容实时对比</p>
        <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <div class="stats">
        <div class="stat-item">
            <div class="stat-value" style="color: #28a745;">97.4%</div>
            <div class="stat-label">静态令牌成功率</div>
        </div>
        <div class="stat-item">
            <div class="stat-value" style="color: #dc3545;">0%</div>
            <div class="stat-label">动态令牌成功率</div>
        </div>
        <div class="stat-item">
            <div class="stat-value" style="color: #007bff;">348,239</div>
            <div class="stat-label">静态响应字符数</div>
        </div>
        <div class="stat-item">
            <div class="stat-value" style="color: #ffc107;">15,190</div>
            <div class="stat-label">动态响应字符数</div>
        </div>
    </div>
    
    <div class="comparison-container">
        <div class="panel">
            <div class="panel-header success">
                ✅ 静态令牌响应 - 成功获取产品页面
                <br><small>Accept-CH: Downlink, DPR | 状态码: 200</small>
            </div>
            <iframe class="content-frame" srcdoc="{static_content.replace('"', '&quot;').replace("'", "&#39;")}"></iframe>
        </div>
        
        <div class="panel">
            <div class="panel-header failure">
                ❌ 动态令牌响应 - 触发PerimeterX验证
                <br><small>Accept-CH: None | 状态码: 200 (但内容被拦截)</small>
            </div>
            <iframe class="content-frame" srcdoc="{dynamic_content.replace('"', '&quot;').replace("'", "&#39;")}"></iframe>
        </div>
    </div>
    
    <div class="analysis">
        <h2>🔬 关键差异分析</h2>
        
        <div class="highlight">
            <h3>静态令牌 (成功)</h3>
            <ul>
                <li><strong>页面标题:</strong> "Fujifilm Instax Wide 400 Instant Film Camera..."</li>
                <li><strong>内容类型:</strong> 完整的产品详情页面</li>
                <li><strong>关键指标:</strong> Accept-CH头存在，包含产品信息</li>
                <li><strong>令牌来源:</strong> 令牌.xlsx预验证令牌池</li>
            </ul>
        </div>
        
        <div class="highlight">
            <h3>动态令牌 (失败)</h3>
            <ul>
                <li><strong>页面标题:</strong> "Robot or human?"</li>
                <li><strong>内容类型:</strong> PerimeterX验证码挑战页面</li>
                <li><strong>关键指标:</strong> 无Accept-CH头，触发反机器人检测</li>
                <li><strong>令牌来源:</strong> 实时HEAD请求动态获取</li>
            </ul>
        </div>
        
        <h3>💡 核心洞察</h3>
        <p><strong>为什么静态令牌有效？</strong></p>
        <ul>
            <li>令牌已经过预验证，具有"可信"状态</li>
            <li>没有可疑的令牌获取行为模式</li>
            <li>符合正常用户访问模式</li>
        </ul>
        
        <p><strong>为什么动态令牌失效？</strong></p>
        <ul>
            <li>实时获取令牌的行为被PerimeterX识别</li>
            <li>新生成的令牌缺乏使用历史</li>
            <li>异常的URL访问模式触发检测</li>
        </ul>
    </div>
</body>
</html>
"""
    
    # 保存HTML文件
    filename = f"token_comparison_{int(datetime.now().timestamp())}.html"
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(comparison_html)
    
    return filename

def open_comparison_page():
    """打开对比页面"""
    filename = create_comparison_html()
    file_path = os.path.abspath(filename)
    
    print(f"🌐 正在打开对比页面: {filename}")
    print(f"📁 文件路径: {file_path}")
    
    # 在默认浏览器中打开
    webbrowser.open(f'file://{file_path}')
    
    print("✅ 对比页面已在浏览器中打开")
    print("\n📋 页面功能:")
    print("- 左侧: 静态令牌的成功响应页面")
    print("- 右侧: 动态令牌的失败响应页面")
    print("- 顶部: 关键统计数据对比")
    print("- 底部: 详细差异分析")
    
    return filename

if __name__ == '__main__':
    open_comparison_page()
